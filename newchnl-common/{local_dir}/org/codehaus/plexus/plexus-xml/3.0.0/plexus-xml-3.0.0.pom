<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>13</version>
  </parent>

  <artifactId>plexus-xml</artifactId>
  <version>3.0.0</version>

  <name>Plexus XML Utilities</name>
  <description>A collection of various utility classes to ease working with XML in Maven 3.</description>
  <url>https://codehaus-plexus.github.io/plexus-xml/</url>

  <scm>
    <connection>scm:git:https://github.com/codehaus-plexus/plexus-xml.git</connection>
    <developerConnection>scm:git:https://github.com/codehaus-plexus/plexus-xml.git</developerConnection>
    <tag>plexus-xml-3.0.0</tag>
    <url>https://github.com/codehaus-plexus/plexus-xml/tree/${project.scm.tag}/</url>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>https://github.com/codehaus-plexus/plexus-xml/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${project.scm.developerConnection}</url>
    </site>
  </distributionManagement>

  <properties>
    <project.build.outputTimestamp>2023-09-11T17:52:31Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>4.0.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>1.36</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>1.36</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <content>${project.reporting.outputDirectory}</content>
          <!-- mono-module doesn't require site:stage -->
        </configuration>
        <executions>
          <execution>
            <id>scm-publish</id>
            <!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
            <phase>site-deploy</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- required to ensure the test classes are used, not surefire's plexus-utils -->
          <childDelegation>true</childDelegation>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>

<!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  ~
  ~  Copyright 2001-2006 The Codehaus Foundation.
  ~
  ~  Licensed under the Apache License, Version 2.0 (the "License");
  ~  you may not use this file except in compliance with the License.
  ~  You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~  Unless required by applicable law or agreed to in writing, software
  ~  distributed under the License is distributed on an "AS IS" BASIS,
  ~  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~  See the License for the specific language governing permissions and
  ~  limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>5.1</version>
  </parent>

  <artifactId>plexus-classworlds</artifactId>
  <version>2.6.0</version>
  <packaging>bundle</packaging>

  <name>Plexus Classworlds</name>
  <description>A class loader framework</description>
  <inceptionYear>2002</inceptionYear>

  <scm>
    <connection>scm:git:**************:codehaus-plexus/plexus-classworlds.git</connection>
    <developerConnection>scm:git:**************:codehaus-plexus/plexus-classworlds.git</developerConnection>
    <url>http://github.com/codehaus-plexus/plexus-classworlds/tree/${project.scm.tag}/</url>
    <tag>plexus-classworlds-2.6.0</tag>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>http://github.com/codehaus-plexus/plexus-classworlds/issues</url>
  </issueManagement>

  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${project.scm.developerConnection}</url>
    </site>
  </distributionManagement>
  
  <properties>
    <javaVersion>7</javaVersion>
  </properties> 

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <configuration>
            <content>${project.reporting.outputDirectory}</content><!-- mono-module doesn't require site:stage -->
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.5</version>
          <configuration>
            <localCheckout>true</localCheckout>
            <pushChanges>false</pushChanges>
            <useReleaseProfile>false</useReleaseProfile>
            <goals>deploy</goals>
            <arguments>-Pplexus-release</arguments>
          </configuration>
        </plugin>        
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <executions>
          <execution>
            <id>scm-publish</id>
            <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>3.5.1</version>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <_nouses>true</_nouses>
            <Export-Package>org.codehaus.classworlds.*;org.codehaus.plexus.classworlds.*</Export-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <mainClass>org.codehaus.plexus.classworlds.launcher.Launcher</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <redirectTestOutputToFile>true</redirectTestOutputToFile>
          <argLine>-ea:org.codehaus.classworlds:org.codehaus.plexus.classworlds</argLine>
          <forkMode>once</forkMode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>org/codehaus/plexus/classworlds/event/*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>2.0</version>
        <executions>
          <execution>
            <phase>generate-test-resources</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>org.apache.ant</groupId>
                  <artifactId>ant</artifactId>
                  <version>1.9.0</version>
                </artifactItem>
                <artifactItem>
                  <groupId>commons-logging</groupId>
                  <artifactId>commons-logging</artifactId>
                  <version>1.0.3</version>
                </artifactItem>
                <artifactItem>
                  <groupId>xml-apis</groupId>
                  <artifactId>xml-apis</artifactId>
                  <version>1.3.02</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/test-lib</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>1.3.1</version>
        <executions>
          <execution>
            <id>enforce-java</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>1.7.0</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <configuration>
          <dependencyLocationsEnabled>false</dependencyLocationsEnabled>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>summary</report>
              <report>index</report>
              <report>dependencies</report>
              <report>issue-tracking</report>
              <report>scm</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.9.1</version>
        <configuration>
          <encoding>utf-8</encoding>
          <attach>true</attach>
          <quiet>true</quiet>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

</project>

<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>10</version>
  </parent>

  <artifactId>plexus-utils</artifactId>
  <version>3.5.0</version>

  <name>Plexus Common Utilities</name>
  <description>A collection of various utility classes to ease working with strings, files, command lines, XML and
    more.
  </description>

  <scm>
    <connection>scm:git:**************:codehaus-plexus/plexus-utils.git</connection>
    <developerConnection>scm:git:**************:codehaus-plexus/plexus-utils.git</developerConnection>
    <url>http://github.com/codehaus-plexus/plexus-utils</url>
    <tag>plexus-utils-3.5.0</tag>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>http://github.com/codehaus-plexus/plexus-utils/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${project.scm.developerConnection}</url>
    </site>
  </distributionManagement>

  <properties>
    <project.build.outputTimestamp>2022-10-23T08:37:59Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>1.35</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>1.35</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <!--
          maven totally change his way to copy symlink
          this mean the symlink is copied as a symlink and not anymore as a new file
          https://issues.apache.org/jira/browse/MRESOURCES-237
          -->
          <version>2.7</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>default-compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <content>${project.reporting.outputDirectory}</content><!-- mono-module doesn't require site:stage -->
        </configuration>
        <executions>
          <execution>
            <id>scm-publish</id>
            <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- required to ensure the test classes are used, not surefire's plexus-utils -->
          <childDelegation>true</childDelegation>
          <excludes>
            <exclude>org/codehaus/plexus/util/FileBasedTestCase.java</exclude>
            <exclude>**/Test*.java</exclude>
          </excludes>
          <systemProperties>
            <property>
              <name>JAVA_HOME</name>
              <value>${JAVA_HOME}</value>
            </property>
            <property>
              <name>M2_HOME</name>
              <value>${M2_HOME}</value>
            </property>
          </systemProperties>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Multi-Release>true</Multi-Release>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>jdk9+</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-compiler-plugin</artifactId>
              <executions>
                <execution>
                  <id>compile-java-9</id>
                  <goals>
                    <goal>compile</goal>
                  </goals>
                  <configuration>
                    <release>9</release>
                    <compileSourceRoots>
                      <compileSourceRoot>${project.basedir}/src/main/java9</compileSourceRoot>
                  </compileSourceRoots>
                  <multiReleaseOutput>true</multiReleaseOutput>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>jdk10+</id>
      <activation>
        <jdk>[10,)</jdk>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-compiler-plugin</artifactId>
              <executions>
                <execution>
                  <id>compile-java-10</id>
                  <goals>
                    <goal>compile</goal>
                  </goals>
                  <configuration>
                    <release>10</release>
                    <compileSourceRoots>
                      <compileSourceRoot>${project.basedir}/src/main/java10</compileSourceRoot>
                  </compileSourceRoots>
                  <multiReleaseOutput>true</multiReleaseOutput>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>plexus-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-java</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireJavaVersion>
                      <version>11</version>
                    </requireJavaVersion>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

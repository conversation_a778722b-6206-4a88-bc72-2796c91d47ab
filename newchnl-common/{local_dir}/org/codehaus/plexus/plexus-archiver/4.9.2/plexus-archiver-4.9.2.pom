<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>17</version>
  </parent>

  <artifactId>plexus-archiver</artifactId>
  <version>4.9.2</version>
  <name>Plexus Archiver Component</name>

  <url>https://codehaus-plexus.github.io/plexus-archiver/</url>

  <contributors>
    <contributor>
      <name><PERSON></name>
    </contributor>
    <contributor>
      <name><PERSON></name>
    </contributor>
    <contributor>
      <name><PERSON><PERSON> '<PERSON><PERSON>' <PERSON><PERSON></name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:https://github.com/codehaus-plexus/plexus-archiver.git</connection>
    <developerConnection>scm:git:https://github.com/codehaus-plexus/plexus-archiver.git</developerConnection>
    <tag>plexus-archiver-4.9.2</tag>
    <url>https://github.com/codehaus-plexus/plexus-archiver/tree/${project.scm.tag}/</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/codehaus-plexus/plexus-archiver/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${project.scm.developerConnection}</url>
    </site>
  </distributionManagement>

  <properties>
    <slf4jVersion>1.7.36</slf4jVersion>
    <project.build.outputTimestamp>2024-03-14T22:25:33Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <!-- JSR330 -->
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
    </dependency>
    <!-- Plexus dependencies -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>4.0.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-io</artifactId>
      <version>3.4.2</version>
    </dependency>
    <!-- Apache Commons dependencies -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.15.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>1.26.1</version>
    </dependency>
    <dependency>
      <!-- For compressors: snappy and lz4 -->
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.16.1</version>
    </dependency>
    <!-- Other dependencies -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4jVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.iq80.snappy</groupId>
      <artifactId>snappy</artifactId>
      <version>0.4</version>
    </dependency>
    <dependency>
      <groupId>org.tukaani</groupId>
      <artifactId>xz</artifactId>
      <version>1.9</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.luben</groupId>
      <artifactId>zstd-jni</artifactId>
      <version>1.5.5-11</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>provided</scope>
    </dependency>
    <!-- Test dependencies -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>${slf4jVersion}</version>
      <scope>test</scope>
    </dependency>
    <!-- Plexus container dependencies -->
    <dependency>
      <groupId>org.eclipse.sisu</groupId>
      <artifactId>org.eclipse.sisu.inject</artifactId>
      <version>${sisuMavenPluginVersion}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>6.0.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.25.3</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <!-- olamy: exclude files with strange names as failed here on osx -->
          <checkModificationExcludes>
            <checkModificationExclude>**/src/test/resources/utf8/**</checkModificationExclude>
          </checkModificationExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>sisu-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <content>${project.reporting.outputDirectory}</content>
          <!-- mono-module doesn't require site:stage -->
        </configuration>
        <executions>
          <execution>
            <id>scm-publish</id>
            <!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
            <phase>site-deploy</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>

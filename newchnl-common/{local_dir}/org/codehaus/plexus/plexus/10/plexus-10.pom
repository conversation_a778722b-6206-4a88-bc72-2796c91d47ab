<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus</artifactId>
  <packaging>pom</packaging>
  <version>10</version>

  <name>Plexus</name>
  <description>The Plexus project provides a full software stack for creating and executing software projects.</description>
  <url>https://codehaus-plexus.github.io/</url>
  <inceptionYear>2001</inceptionYear>
  <organization>
    <name>Codehaus Plexus</name>
    <url>https://codehaus-plexus.github.io/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
        <role>Release Manager</role>
      </roles>
    </developer>
    <developer>
      <id>kaz</id>
      <name>Pete Kazmier</name>
      <email />
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jtaylor</id>
      <name>James Taylor</name>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dandiep</id>
      <name>Dan Diephouse</name>
      <email><EMAIL></email>
      <organization>Envoi solutions</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kasper</id>
      <name>Kasper Nielsen</name>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bwalding</id>
      <name>Ben Walding</name>
      <email><EMAIL></email>
      <organization>Walding Consulting Services</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mhw</id>
      <name>Mark Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michal</id>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Trygve Laugstøl</name>
      <id>trygvis</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Kenney Westerhof</name>
      <id>kenney</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Carlos Sanchez</name>
      <id>carlos</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Brett Porter</name>
      <id>brett</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>John Casey</name>
      <id>jdcasey</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Andrew Williams</name>
      <id>handyande</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Rahul Thakur</name>
      <id>rahul</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Joakim Erdfelt</name>
      <id>joakime</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Olivier Lamy</name>
      <id>olamy</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Hervé Boutemy</name>
      <id>hboutemy</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Oleg Gusakov</name>
      <id>oleg</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Vincent Siveton</name>
      <id>vsiveton</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Kristian Rosenvold</name>
      <id>krosenvold</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Andreas Gudian</name>
      <id>agudian</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Karl Heinz Marbaise</name>
      <id>khmarbaise</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Michael Osipov</name>
      <id>michael-o</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Gabriel Belingueres</name>
      <id>belingueres</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <mailingLists><!-- TODO decide what we do with mailing lists now -->
    <mailingList>
      <name>Plexus User List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/user%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/user%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/user</archive>
      <post><EMAIL></post>
    </mailingList>
    <mailingList>
      <name>Plexus Developer List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/dev%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/dev%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/dev</archive>
      <post><EMAIL></post>
    </mailingList>
    <mailingList>
      <name>Plexus Announce List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/announce%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/announce%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/announce</archive>
    </mailingList>
    <mailingList>
      <name>Plexus Commit List</name>
      <subscribe>http://xircles.codehaus.org/manage_email/scm%40plexus.codehaus.org</subscribe>
      <unsubscribe>http://xircles.codehaus.org/manage_email/scm%40plexus.codehaus.org</unsubscribe>
      <archive>http://archive.plexus.codehaus.org/scm</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:**************:codehaus-plexus/plexus-pom.git</connection>
    <developerConnection>scm:git:**************:codehaus-plexus/plexus-pom.git</developerConnection>
    <url>https://github.com/codehaus-plexus/plexus-pom/tree/${project.scm.tag}/</url>
    <tag>plexus-10</tag>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>https://github.com/codehaus-plexus/plexus-pom/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>plexus-releases</id>
      <name>Plexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>plexus-snapshots</id>
      <name>Plexus Snapshot Repository</name>
      <url>${plexusDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>github:gh-pages</id>
      <url>scm:git:**************:codehaus-plexus</url><!-- url used only for inheritance -->
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>8</javaVersion>
    <maven.compiler.source>1.${javaVersion}</maven.compiler.source>
    <maven.compiler.target>1.${javaVersion}</maven.compiler.target>    
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <plexusDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/plexus-snapshots</plexusDistMgmtSnapshotsUrl>
    <project.build.outputTimestamp>2022-06-09T20:48:05Z</project.build.outputTimestamp>
    <gpg.useagent>true</gpg.useagent>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>2.1.1</version>
        <scope>compile</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <!-- set versions of common plugins for reproducibility, ordered alphabetically -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.1.2</version>
          <configuration>
            <configLocation>config/maven_checks.xml</configLocation>
            <headerLocation>https://raw.github.com/codehaus-plexus/plexus-pom/master/src/main/resources/config/plexus-header.txt</headerLocation>
          </configuration>
          <dependencies>
            <!-- can be removed with update to 3.2.x -->
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>9.3</version>
            </dependency>
            <!-- MCHECKSTYLE-327: the maven_checks.xml was moved to a shared project -->
            <dependency>
              <groupId>org.apache.maven.shared</groupId>
              <artifactId>maven-shared-resources</artifactId>
              <version>4</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.10.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.8.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.2.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.4.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>3.6.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>3.17.0</version>
          <configuration>
            <targetJdk>${maven.compiler.source}</targetJdk>
            <rulesets>
              <ruleset>rulesets/maven.xml</ruleset>
            </rulesets>
            <excludeRoots>
              <excludeRoot>${project.build.directory}/generated-sources/modello</excludeRoot>
              <excludeRoot>${project.build.directory}/generated-sources/plugin</excludeRoot>
            </excludeRoots>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.0-M6</version>
          <configuration>
            <goals>deploy</goals>
            <mavenExecutorId>forked-path</mavenExecutorId>
            <releaseProfiles>plexus-release</releaseProfiles>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>3.1.0</version>
          <configuration>
            <!-- using scm.developerConnection instead of distributionManagement.site.url -->
            <pubScmUrl>${project.scm.developerConnection}</pubScmUrl>
            <scmBranch>gh-pages</scmBranch>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.0</version>
          <configuration>
            <skipDeploy>true</skipDeploy><!-- don't deploy site with maven-site-plugin -->
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>2.22.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.5</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>2.1.1</version>
          <executions>
            <execution>
              <id>process-classes</id>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
            <execution>
              <id>process-test-classes</id>
              <goals>
                <goal>generate-test-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0.5</version>
                  <message>This project requires at least Maven 3.0.5</message>
                </requireMavenVersion>
            </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>summary</report>
              <report>dependency-info</report>
              <report>modules</report>
              <report>licenses</report>
              <report>team</report>
              <report>scm</report>
              <report>issue-management</report>
              <report>mailing-lists</report>
              <report>dependency-management</report>
              <report>dependencies</report>
              <report>dependency-convergence</report>
              <report>ci-management</report>
              <report>plugin-management</report>
              <report>plugins</report>
              <report>distribution-management</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-report-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>default</id>
                <reports>
                  <report>checkstyle</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>findbugs-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>taglist-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>default</id>
                <reports>
                  <report>jxr</report>
                  <report>test-jxr</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <detectLinks>true</detectLinks>
              <links>
                <link>http://junit.sourceforge.net/javadoc/</link>
              </links>
            </configuration>
            <reportSets>
              <reportSet>
                <id>default</id>
                <reports>
                  <report>javadoc</report>
                  <report>test-javadoc</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>plexus-release</id>
      <build>
        <plugins>
          <!-- Create a source-release artifact that contains the fully buildable
               project directory source structure. -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.0.6</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileMode>posix</tarLongFileMode>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>pre-JEP_247</id>
      <activation>
        <jdk>[7,8]</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>animal-sniffer-enforcer-rule</artifactId>
                <version>1.21</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>check-signatures</id>
                <phase>test</phase>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <checkSignatureRule implementation="org.codehaus.mojo.animal_sniffer.enforcer.CheckSignatureRule">
                      <signature>
                        <groupId>org.codehaus.mojo.signature</groupId>
                        <artifactId>java1${javaVersion}</artifactId>
                        <version>1.0</version>
                      </signature>
                    </checkSignatureRule>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

<?xml version="1.0" encoding="UTF-8"?>
  <!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements. See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version
    2.0 (the "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0 Unless required by
    applicable law or agreed to in writing, software distributed under
    the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES
    OR CONDITIONS OF ANY KIND, either express or implied. See the
    License for the specific language governing permissions and
    limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.fusesource.hawtbuf</groupId>
    <artifactId>hawtbuf-project</artifactId>
    <version>1.11</version>
  </parent>

  <groupId>org.fusesource.hawtbuf</groupId>
  <artifactId>hawtbuf</artifactId>
  <version>1.11</version>
  <packaging>bundle</packaging>

  <name>${project.artifactId}</name>
  <properties>
     <!-- The ObjectInputStream need to load the class outside of the bundle -->
     <hawtbuf.osgi.dynamic>*</hawtbuf.osgi.dynamic>
  </properties>

  <dependencies>

    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>${commons-logging-version}</version>
      <optional>true</optional>
    </dependency>
    
    <!-- Testing Dependencies -->    
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit-version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <scope>test</scope>
      <version>${log4j-version}</version>
    </dependency>
      
  </dependencies>
  
  <build>
    <plugins>
      
      <!-- Generate a test jar for the test cases in this package -->
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>  

</project>

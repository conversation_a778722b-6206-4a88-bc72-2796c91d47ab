<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>hibernate-validator-parent</artifactId>
    <groupId>org.hibernate</groupId>
    <version>4.2.0.Final</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.hibernate</groupId>
  <artifactId>hibernate-validator</artifactId>
  <name>Hibernate Validator</name>
  <description>Hibernate's Bean Validation (JSR-303) reference implementation.</description>
  <build>
    <defaultGoal>test</defaultGoal>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>src/main/xsd</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <filtering>true</filtering>
        <directory>src/test/resources</directory>
        <includes>
          <include>**/*.properties</include>
          <include>**/*.xml</include>
        </includes>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jaxb2-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>xjc</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <packageName>org.hibernate.validator.xml</packageName>
          <outputDirectory>${basedir}/target/generated-sources</outputDirectory>
          <extension>true</extension>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Import-Package>javax.persistence.*;version="[2.0.0,3.0.0)";resolution:=optional,
                            javax.validation.*;version="[1.0.0,2.0.0)",
                            javax.xml.*;version="0",
                            org.xml.sax.*;version="0",
                            org.slf4j.*;version="[1.5.6,2.0.0)",
                            org.joda.time.*;version="[1.6.0,2.0.0)";resolution:=optional,
                            org.jsoup.*;version="[1.5.2,2.0.0)";resolution:=optional</Import-Package>
            <Export-Package>org.hibernate.validator;version="${project.version}",
                            org.hibernate.validator.constraints;version="${project.version}",
                            org.hibernate.validator.cfg;version="${project.version}",
                            org.hibernate.validator.cfg.context;version="${project.version}",
                            org.hibernate.validator.cfg.defs;version="${project.version}",
                            org.hibernate.validator.group;version="${project.version}",
                            org.hibernate.validator.messageinterpolation;version="${project.version}",
                            org.hibernate.validator.method;version="${project.version}",
                            org.hibernate.validator.method.metadata;version="${project.version}",
                            org.hibernate.validator.resourceloading;version="${project.version}"</Export-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <suiteXmlFiles>
            <suiteXmlFile>${basedir}/src/test/suite/unit-tests.xml</suiteXmlFile>
          </suiteXmlFiles>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>com.googlecode.jtype:jtype</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>com.googlecode.jtype</pattern>
                  <shadedPattern>org.hibernate.validator.jtype</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer />
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <phase>deploy</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <links>
            <link>http://java.sun.com/j2se/1.5.0/docs/api/</link>
            <link>http://docs.jboss.org/hibernate/stable/beanvalidation/api/</link>
          </links>
          <packagesheader>Hibernate Validator Packages</packagesheader>
          <doctitle>Hibernate Validator ${project.version}</doctitle>
          <windowtitle>Hibernate Validator ${project.version}</windowtitle>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jboss.maven.plugins</groupId>
        <artifactId>maven-jdocbook-plugin</artifactId>
        <extensions>true</extensions>
        <executions>
          <execution>
            <id>make-doc</id>
            <phase>deploy</phase>
            <goals>
              <goal>resources</goal>
              <goal>translate</goal>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jdocbook-style</artifactId>
            <version>2.0.1</version>
            <type>jdocbook-style</type>
          </dependency>
        </dependencies>
        <configuration>
          <sourceDocumentName>master.xml</sourceDocumentName>
          <sourceDirectory>${basedir}/src/main/docbook</sourceDirectory>
          <masterTranslation>en-US</masterTranslation>
          <translations>
            <translation>zh-CN</translation>
          </translations>
          <ignoreTranslations>${jdocbook.ignoreTranslations}</ignoreTranslations>
          <imageResource>
            <directory>${basedir}/src/main/docbook/en-US/images</directory>
          </imageResource>
          <formats>
            <format>
              <formatName>pdf</formatName>
              <stylesheetResource>classpath:/xslt/org/hibernate/jdocbook/xslt/pdf.xsl</stylesheetResource>
              <finalName>hibernate_validator_reference.pdf</finalName>
            </format>
            <format>
              <formatName>html_single</formatName>
              <stylesheetResource>classpath:/xslt/org/hibernate/jdocbook/xslt/xhtml-single.xsl</stylesheetResource>
              <finalName>index.html</finalName>
            </format>
            <format>
              <formatName>html</formatName>
              <stylesheetResource>classpath:/xslt/org/hibernate/jdocbook/xslt/xhtml.xsl</stylesheetResource>
              <finalName>index.html</finalName>
            </format>
          </formats>
          <options>
            <xincludeSupported>true</xincludeSupported>
            <xmlTransformerType>saxon</xmlTransformerType>
            <docbookVersion>1.72.0</docbookVersion>
            <localeSeparator>-</localeSeparator>
          </options>
          <profiling>
            <enabled>true</enabled>
          </profiling>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jboss.maven.plugins</groupId>
        <artifactId>maven-jdocbook-style-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jboss.maven.plugins</groupId>
        <artifactId>maven-injection-plugin</artifactId>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>bytecode</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <bytecodeInjections>
            <bytecodeInjection>
              <expression>${project.version}</expression>
              <targetMembers>
                <methodBodyReturn>
                  <className>org.hibernate.validator.util.Version</className>
                  <methodName>getVersionString</methodName>
                </methodBodyReturn>
              </targetMembers>
            </bytecodeInjection>
          </bytecodeInjections>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>1.0.0.GA</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.6.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.5.2</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>stax-api</artifactId>
          <groupId>javax.xml.stream</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activation</artifactId>
          <groupId>javax.activation</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <version>2.1.12</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.javax.persistence</groupId>
      <artifactId>hibernate-jpa-2.0-api</artifactId>
      <version>1.0.1.Final</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>1.6</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <version>5.14.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>bsh</artifactId>
          <groupId>org.beanshell</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guice</artifactId>
          <groupId>com.google.inject</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jcommander</artifactId>
          <groupId>com.beust</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <version>3.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>cglib-nodep</artifactId>
          <groupId>cglib</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-entitymanager</artifactId>
      <version>3.6.0.Final</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hibernate-core</artifactId>
          <groupId>org.hibernate</groupId>
        </exclusion>
        <exclusion>
          <artifactId>cglib</artifactId>
          <groupId>cglib</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javassist</artifactId>
          <groupId>javassist</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>1.2.124</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-log4j12</artifactId>
      <version>1.6.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <distributionManagement>
    <site>
      <id>site</id>
      <url>http://validator.hibernate.org</url>
    </site>
  </distributionManagement>
  <properties>
    <jdbc.url>jdbc:h2:mem:db1;DB_CLOSE_DELAY=-1</jdbc.url>
    <db.dialect>org.hibernate.dialect.H2Dialect</db.dialect>
    <jdbc.driver>org.h2.Driver</jdbc.driver>
    <jdbc.user>sa</jdbc.user>
  </properties>
</project>


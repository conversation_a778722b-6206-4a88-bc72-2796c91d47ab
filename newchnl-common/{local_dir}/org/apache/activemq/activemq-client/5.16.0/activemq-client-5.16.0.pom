<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.activemq</groupId>
    <artifactId>activemq-parent</artifactId>
    <version>5.16.0</version>
  </parent>

  <artifactId>activemq-client</artifactId>
  <packaging>bundle</packaging>
  <name>ActiveMQ :: Client</name>
  <description>The ActiveMQ Client implementation</description>

  <properties>
    <surefire.argLine>-Xmx512M</surefire.argLine>
    <activemq.osgi.import.pkg>
      !com.google.errorprone.annotations,
      !com.google.errorprone.annotations.concurrent,
      *
    </activemq.osgi.import.pkg>
    <activemq.osgi.private.pkg>
      com.google.errorprone.annotations,
      com.google.errorprone.annotations.concurrent
    </activemq.osgi.private.pkg>
  </properties>

  <dependencies>
    <!-- =============================== -->
    <!-- Required Dependencies -->
    <!-- =============================== -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.geronimo.specs</groupId>
      <artifactId>geronimo-jms_1.1_spec</artifactId>
    </dependency>
    <dependency>
      <groupId>org.fusesource.hawtbuf</groupId>
      <artifactId>hawtbuf</artifactId>
      <version>${hawtbuf-version}</version>
    </dependency>

    <!-- would be nice if we could make this dependency optional -->
    <dependency>
      <groupId>org.apache.geronimo.specs</groupId>
      <artifactId>geronimo-j2ee-management_1.1_spec</artifactId>
    </dependency>

    <!-- for ftp blob upload/download -->
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- for zerconf discovery -->
    <dependency>
      <groupId>javax.jmdns</groupId>
      <artifactId>jmdns</artifactId>
      <optional>true</optional>
    </dependency>

    <!-- =============================== -->
    <!-- Testing Dependencies -->
    <!-- =============================== -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-log4j12</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin-version}</version>
        <configuration>
          <!-- necessary for now under the javadocs can be fixed because jdk8 is much stricter -->
          <additionalparam>${javadoc.options}</additionalparam>
          <links>
            <link>${javase-javadoc-url}</link>
            <link>${javaee-javadoc-url}</link>
            <link>${slf4j-javadoc-url}</link>
            <link>http://junit.sourceforge.net/javadoc/</link>
          </links>
          <stylesheetfile>${basedir}/../etc/css/stylesheet.css</stylesheetfile>
          <linksource>true</linksource>
          <maxmemory>256m</maxmemory>
          <source>${source-version}</source>
          <groups>
            <group>
              <title>JMS Client</title>
              <packages>org.apache.activemq:org.apache.activemq.command</packages>
            </group>
            <group>
              <title>JMS Client support classes for working with BLOBs and JNDI</title>
              <packages>org.apache.activemq.blob:org.apache.activemq.jndi</packages>
            </group>
            <group>
              <title>Enterprise Integration Pattern support via Camel</title>
              <packages>org.apache.activemq.camel:org.apache.activemq.camel.*</packages>
            </group>
            <group>
              <title>Spring support</title>
              <packages>org.apache.activemq.spring:org.apache.activemq.xbean:org.apache.activemq.pool</packages>
            </group>
            <group>
              <title>JMS Client Implementation classes</title>
              <packages>org.apache.activemq.filter:org.apache.activemq.management:org.apache.activemq.selector:org.apache.activemq.thread</packages>
            </group>
            <group>
              <title>Broker implementation</title>
              <packages>org.apache.activemq.advisory:org.apache.activemq.broker:org.apache.activemq.broker.*:org.apache.activemq.state:org.apache.activemq.security:org.apache.activemq.transaction</packages>
            </group>
            <group>
              <title>Broker Persistent Store</title>
              <packages>org.apache.activemq.store:org.apache.activemq.store.*</packages>
            </group>
            <group>
              <title>Kaha fast file based storage</title>
              <packages>org.apache.activemq.kaha:org.apache.activemq.kaha.*</packages>
            </group>
            <group>
              <title>Transports</title>
              <packages>org.apache.activemq.transport:org.apache.activemq.transport.*</packages>
            </group>
            <group>
              <title>Memory handling</title>
              <packages>org.apache.activemq.memory:org.apache.activemq.memory.*</packages>
            </group>
            <group>
              <title>Networks of Brokers</title>
              <packages>org.apache.activemq.network:org.apache.activemq.network.*</packages>
            </group>
            <group>
              <title>OpenWire support</title>
              <packages>org.apache.activemq.openwire:org.apache.activemq.openwire.*</packages>
            </group>
            <group>
              <title>Utility classes</title>
              <packages>org.apache.activemq.util:org.apache.activemq.wireformat:org.apache.activemq.proxy</packages>
            </group>
          </groups>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${findbugs-maven-plugin-version}</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <build>
    <resources>
      <resource>
        <directory>${project.basedir}/src/main/resources</directory>
        <includes>
          <include>**/*</include>
        </includes>
      </resource>
      <resource>
        <directory>${project.basedir}/src/main/filtered-resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>**/*</include>
        </includes>
      </resource>
    </resources>

    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <forkCount>1</forkCount>
          <reuseForks>false</reuseForks>
          <argLine>${surefire.argLine}</argLine>
          <runOrder>alphabetical</runOrder>
          <systemProperties>
            <property>
              <name>org.apache.activemq.default.directory.prefix</name>
              <value>target/</value>
            </property>
            <!-- Uncomment the following if you want to configure custom logging
              (using src/test/resources/log4j.properties) while running mvn:test Note: if you want
              to see log messages on the console window remove "redirectTestOutputToFile" from
              the parent pom -->
            <!-- <property> <name>log4j.configuration</name> <value>file:target/test-classes/log4j.properties</value>
              </property> -->
          </systemProperties>
          <includes>
            <include>**/*Test.*</include>
          </includes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javacc-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <configuration>
              <sourceDirectory>${basedir}/src/main/grammar</sourceDirectory>
              <outputDirectory>${basedir}/target/generated-sources/javacc</outputDirectory>
              <packageName>org.apache.activemq.selector</packageName>
            </configuration>
            <goals>
              <goal>javacc</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <executions>
              <execution>
                  <id>add-source</id>
                  <phase>generate-sources</phase>
                  <goals>
                      <goal>add-source</goal>
                  </goals>
                  <configuration>
                      <sources>
                          <source>${basedir}/target/generated-sources/javacc</source>
                      </sources>
                  </configuration>
              </execution>
          </executions>
      </plugin>
    </plugins>
    <pluginManagement>
        <plugins>
            <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
            <plugin>
                <groupId>org.eclipse.m2e</groupId>
                <artifactId>lifecycle-mapping</artifactId>
                <version>1.0.0</version>
                <configuration>
                    <lifecycleMappingMetadata>
                        <pluginExecutions>
                            <pluginExecution>
                                <pluginExecutionFilter>
                                    <groupId>org.codehaus.mojo</groupId>
                                    <artifactId>
                                        javacc-maven-plugin
                                    </artifactId>
                                    <versionRange>[2.6,)</versionRange>
                                    <goals>
                                        <goal>javacc</goal>
                                    </goals>
                                </pluginExecutionFilter>
                                <action>
                                    <execute>
                                      <runOnIncremental>true</runOnIncremental>
                                    </execute>
                                </action>
                            </pluginExecution>
                        </pluginExecutions>
                    </lifecycleMappingMetadata>
                </configuration>
            </plugin>
        </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <!-- Execute with: mvn -P openwire-generate antrun:run -->
    <profile>
      <id>openwire-generate</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>default</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>run</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <tasks>
                <echo>Running OpenWire Generator</echo>
                <taskdef name="generate" classname="org.apache.activemq.openwire.tool.JavaGeneratorTask" classpathref="maven.compile.classpath" />
                <generate version="12" basedir="${basedir}" generateTests="false" />
              </tasks>
            </configuration>
            <dependencies>
                <dependency>
                    <groupId>org.apache.activemq</groupId>
                    <artifactId>activemq-openwire-generator</artifactId>
                    <version>${project.version}</version>
                </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>activemq.tests-sanity</id>
      <activation>
        <property>
          <name>activemq.tests</name>
          <value>smoke</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>**/MemoryUsageConcurrencyTest.*</exclude>
                <exclude>**/BitArrayBinTest.*</exclude>
                <exclude>**/LRUCacheTest.*</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
	<profile>
      <id>activemq.tests-autoTransport</id>
      <activation>
        <property>
          <name>activemq.tests</name>
          <value>autoTransport</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>**</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

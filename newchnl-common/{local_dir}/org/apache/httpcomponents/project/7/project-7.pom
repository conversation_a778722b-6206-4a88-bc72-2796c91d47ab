<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
   ====================================================================

   This software consists of voluntary contributions made by many
   individuals on behalf of the Apache Software Foundation.  For more
   information on the Apache Software Foundation, please see
   <http://www.apache.org/>.
 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>13</version>
  </parent>
  <groupId>org.apache.httpcomponents</groupId>
  <artifactId>project</artifactId>
  <version>7</version>
  <packaging>pom</packaging>
  <name>HttpComponents</name>
  <url>http://hc.apache.org/</url>
  <description>Components to build HTTP enabled services</description>
  <inceptionYear>2005</inceptionYear>


<!--
Version 7 - changes since version 6
===================================

Fixed maven.compile.* properties => maven.compiler.*
Unfortunately cannot drop the configuration of compiler plugin as the Apache POM
defines the source/target directly rather than using properties.
See: https://issues.apache.org/jira/browse/MPOM-44

Also maven.compile.deprecation => maven.compiler.showDeprecation

** N.B. Child POMs may need to be adjusted when upgrading to parent pom 7 **

Added Maven pre-requisite of 3.0.3 (minimum)
Dropped maven-3 profile as no longer needed
Added Apache parent POM 13; dropped distributionManagement/repository and snapshotRepository
apache.website now uses property ${hc.site.url}
Added optional clover profile

Assembly 2.2.1 => 2.4
Antrun 1.6 => 1.7
Buildnumber 1.0 => 1.2
Bundle 2.3.7 => 2.4.0
Clean 2.4.1 => 2.5
Clirr 2.3 => 2.5
Clover2 2.6.3 => 3.1.11
Compiler 2.3.2 => 3.1
Dokbkx 2.0.13 => 2.0.14
Install 2.3.1 => 2.4
Jar 2.3.2 => 2.4
Javadoc 2.8 => 2.9.1
Project-Info 2.4 => 2.7
Release 2.2.1 => 2.4.1
Resources 2.5 => 2.6
Site 3.0 => 3.3
Source 2.1.2 => 2.2.1
Surefire 2.9 => 2.15
Surefire-Report 2.9 => 2.15
Stylecheck = 2.9.1

Changed issueManagement url to point to JIRA group

Updated developers list

Changes since previous version (5)
==============================

Added httpasyncclient module

Buildnumber plugin changes:
 - can now be disabled (-DbuildNumber.skip)
 - defaults to SVN CLI, because javasvn currently fails with SVN 1.7 clients
 - can revert to javasvn with -Pjavasvn

Added default manifest entries to source jars

Felix bundle plugin updated: 2.3.5 -> 2.3.7 (fixes Java 1.5 issue) 
 -->

  <organization>
    <name>Apache Software Foundation</name>
    <url>http://www.apache.org/</url>
  </organization>

  <issueManagement>
    <system>Jira</system>
    <!-- The following URL is for the HttpComponents group -->
    <url>https://issues.apache.org/jira/secure/BrowseProjects.jspa#10280</url>
  </issueManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/httpcomponents/project/tags/7</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/httpcomponents/tags/7</developerConnection>
    <url>http://svn.apache.org/repos/asf/httpcomponents/tags/7</url>
  </scm>

  <developers>
    <developer>
      <name>Ortwin Glueck</name>
      <id>oglueck</id>
      <email>oglueck -at- apache.org</email>
      <organization></organization>
      <roles>
        <role>Emeritus PMC</role>
      </roles>
      <url>http://www.odi.ch/</url>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Oleg Kalnichevski</name>
      <id>olegk</id>
      <email>olegk -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Asankha C. Perera</name>
      <id>asankha</id>
      <email>asankha -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC Chair</role>
      </roles>
      <url>http://www.adroitlogic.org/</url>
      <timezone>+5.5</timezone>
    </developer>
    <developer>
      <name>Sebastian Bazley</name>
      <id>sebb</id>
      <email>sebb -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC</role>
      </roles>
      <timezone></timezone>
    </developer>
    <developer>
      <name>Erik Abele</name>
      <id>erikabele</id>
      <email>erikabele -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC</role>
      </roles>
      <url>http://www.codefaktor.de/</url>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Ant Elder</name>
      <id>antelder</id>
      <email>antelder -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC</role>
      </roles>
      <timezone></timezone>
    </developer>
    <developer>
      <name>Paul Fremantle</name>
      <id>pzf</id>
      <email>pzf -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC</role>
      </roles>
      <timezone></timezone>
    </developer>
    <developer>
      <name>Roland Weber</name>
      <id>rolandw</id>
      <email>rolandw -at- apache.org</email>
      <roles>
        <role>Emeritus PMC</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Sam Berlin</name>
      <id>sberlin</id>
      <email>sberlin -at- apache.org</email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-4</timezone>
    </developer>
    <developer>
      <name>Sean C. Sullivan</name>
      <id>sullis</id>
      <email>sullis -at- apache.org</email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <name>Jonathan Moore</name>
      <id>jonm</id>
      <email>jonm -at- apache.org</email>
      <roles>
        <role>Committer</role>
        <role>PMC</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <name>Gary Gregory</name>
      <id>ggregory</id>
      <email>ggregory -at- apache.org</email>
      <timezone>-5</timezone>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <name>William Speirs</name>
      <id>wspeirs</id>
      <email>wspeirs at apache.org</email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <name>Karl Wright</name>
      <id>kwright</id>
      <email>kwright -at- apache.org</email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <name>Francois-Xavier Bonnet</name>
      <id>fx</id>
      <email>fx -at- apache.org</email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Julius Davies</name>
      <email>juliusdavies -at- cucbc.com</email>
    </contributor>
    <contributor>
      <name>Andrea Selva</name>
      <email>selva.andre -at- gmail.com</email>
    </contributor>
    <contributor>
      <name>Steffen Pingel</name>
      <email>spingel -at- limewire.com</email>
    </contributor>
    <contributor>
      <name>Quintin Beukes</name>
      <email>quintin -at- last.za.net</email>
    </contributor>
    <contributor>
      <name>Marc Beyerle</name>
      <email>marc.beyerle -at- de.ibm.com</email>
    </contributor>
    <contributor>
      <name>James Abley</name>
      <email>james.abley -at- gmail.com</email>
    </contributor>
    <contributor>
      <name>Michajlo Matijkiw</name>
      <email>michajlo_matijkiw -at- comcast.com</email>
    </contributor>
  </contributors>
  <mailingLists>
    <mailingList>
      <name>HttpClient User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/hc-httpclient-users/</archive>
      <otherArchives>
        <otherArchive>http://www.nabble.com/HttpClient-User-f20180.html</otherArchive>
        <otherArchive>http://marc.info/?l=httpclient-users</otherArchive>
        <otherArchive>http://httpclient-users.markmail.org/search/</otherArchive>
        <otherArchive>http://hc.apache.org/mail/httpclient-users/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>HttpComponents Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/hc-dev/</archive>
      <otherArchives>
        <otherArchive>http://www.nabble.com/HttpComponents-Dev-f20179.html</otherArchive>
        <otherArchive>http://marc.info/?l=httpclient-commons-dev</otherArchive>
        <otherArchive>http://apache-hc-dev.markmail.org/search/</otherArchive>
        <otherArchive>http://hc.apache.org/mail/dev/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>HttpComponents Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post>(Read Only)</post>
      <archive>http://mail-archives.apache.org/mod_mbox/hc-commits/</archive>
      <otherArchives>
        <otherArchive>http://marc.info/?l=httpcomponents-commits</otherArchive>
        <otherArchive>http://hc-commits.markmail.org/search/</otherArchive>
        <otherArchive>http://hc.apache.org/mail/commits/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Apache Announce List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/www-announce/</archive>
      <otherArchives>
        <otherArchive>http://org-apache-announce.markmail.org/search/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

 <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache HttpComponents Website</name>
      <url>${hc.site.url}</url>
    </site>
  </distributionManagement>

  <repositories>
    <!-- allow snapshot dependencies to be resolved -->
    <repository>
      <id>apache.snapshots</id>
      <name>Apache Snapshot Repository</name>
      <url>http://repository.apache.org/snapshots</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
  </repositories>

  <build>
    <plugins>
    <!--
        Override the Apache POM which defines the source/target directly rather than using properties.
         See: https://issues.apache.org/jira/browse/MPOM-44
     -->
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Specification-Title>Apache ${project.name}</Specification-Title>
              <Specification-Version>${project.version}</Specification-Version>
              <Specification-Vendor>Apache Software Foundation</Specification-Vendor>
              <Implementation-Title>Apache HttpComponents ${project.name}</Implementation-Title>
              <Implementation-Version>${project.version}</Implementation-Version>
              <Implementation-Vendor>Apache Software Foundation</Implementation-Vendor>
              <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
              <!-- from buildnumber plugin and properties -->
              <Implementation-Build>${implementation.build}</Implementation-Build>
              <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
              <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <!-- org.apache.maven.plugins, alpha order by artifact id -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.4</version>
          <executions>
            <execution>
              <id>sign-artifacts</id>
              <phase>verify</phase>
              <goals>
                <goal>sign</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${hc.javadoc.version}</version>
          <configuration>
            <!-- reduce console output. Can override with -Dquiet=false -->
            <quiet>true</quiet>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${hc.jxr.version}</version>
        </plugin>
        <plugin>
           <groupId>org.apache.maven.plugins</groupId>
           <artifactId>maven-project-info-reports-plugin</artifactId>
           <version>${hc.project-info.version}</version><!-- needed for direct goal use -->
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.6</version>
          <executions>
            <execution>
              <id>copy-resources</id>
              <phase>pre-site</phase>
              <goals>
                <goal>copy-resources</goal>
              </goals>
              <configuration>
                <outputDirectory>${basedir}/target/site/examples</outputDirectory>
                <resources>          
                  <resource>
                    <directory>src/examples</directory>
                    <filtering>false</filtering>
                  </resource>
                </resources>              
              </configuration>            
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.3</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.wagon</groupId>
              <artifactId>wagon-ssh</artifactId>
              <version>2.0</version>
            </dependency>
          </dependencies>
          <executions>
            <execution>
              <id>attach-descriptor</id>
              <goals>
                <goal>attach-descriptor</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.2.1</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${hc.surefire.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>${hc.surefire-report.version}</version>
        </plugin>
        <!-- Other plugins, alpha order by groupId and artifactId -->
        <plugin>
          <groupId>com.agilejava.docbkx</groupId>
          <artifactId>docbkx-maven-plugin</artifactId>
          <version>2.0.14</version>
          <dependencies>
            <dependency>
              <groupId>org.docbook</groupId>
              <artifactId>docbook-xml</artifactId>
              <version>4.4</version>
              <scope>runtime</scope>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.4.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-notice-plugin</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
          <version>0.0.2</version>
          <executions>
            <execution>
              <id>attach-notice-license</id>
              <goals>
                <goal>generate</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <projectTitle>Apache HttpComponents</projectTitle>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${hc.clirr.version}</version>
          <configuration>
            <minSeverity>${minSeverity}</minSeverity>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>2.9.1</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.httpcomponents</groupId>
              <artifactId>hc-stylecheck</artifactId>
              <version>1</version>
            </dependency>
          </dependencies>
          <configuration>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
          <executions>
            <execution>
              <id>validate</id>
              <phase>validate</phase>
              <configuration>
                <configLocation>hc-stylecheck/default.xml</configLocation>
                <headerLocation>hc-stylecheck/asl2.header</headerLocation>
                <consoleOutput>true</consoleOutput>
                <failsOnError>true</failsOnError>
                <linkXRef>false</linkXRef>
              </configuration>
              <goals>
                <goal>checkstyle</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
        <plugin>
           <groupId>org.apache.maven.plugins</groupId>
           <artifactId>maven-project-info-reports-plugin</artifactId>
           <version>${hc.project-info.version}</version><!-- needed for mvn site -->
           <!-- in particular, we don't want mailing-list to be inherited -->
           <inherited>false</inherited>
           <reportSets>
             <reportSet>
               <reports>
                 <report>project-team</report>
                 <report>issue-tracking</report>
                 <report>scm</report>
                 <report>mailing-list</report>
               </reports>
             </reportSet>
           </reportSets>
         </plugin>
    </plugins>
  </reporting>

 <profiles>
   <profile>
     <id>release</id>
     <build>
       <plugins>
         <plugin>
           <groupId>org.apache.maven.plugins</groupId>
           <artifactId>maven-gpg-plugin</artifactId>
         </plugin>
       </plugins>
     </build>
   </profile>
    <!-- 
     | Profile to allow testing of deploy phase
     | e.g.
     | mvn deploy -Ptest-deploy -Prelease -Dgpg.skip
     -->
    <profile>
      <id>test-deploy</id>
      <properties>
        <altDeploymentRepository>id::default::file:target/deploy</altDeploymentRepository>
      </properties>
    </profile>

    <!-- 
        Automatically run the buildnumber plugin unless the buildNumber.skip property is defined as true
    -->
    <profile>
     <id>svn-buildnumber</id>
     <activation>
       <property><name>!buildNumber.skip</name><value>!true</value></property>
     </activation>
     <build>
       <plugins>
         <plugin>
           <groupId>org.codehaus.mojo</groupId>
           <artifactId>buildnumber-maven-plugin</artifactId>
           <executions>
             <execution>
               <phase>generate-resources</phase>
               <goals>
                 <goal>create</goal>
               </goals>
             </execution>
           </executions>
           <configuration>
             <!-- Use committed revision so it does not change every time svn update is run -->
             <useLastCommittedRevision>true</useLastCommittedRevision>
             <!-- default revision number if unavailable -->
             <revisionOnScmFailure>??????</revisionOnScmFailure>
             <doCheck>false</doCheck>
             <doUpdate>false</doUpdate>
           </configuration>
         </plugin>
       </plugins>
     </build>
   </profile>

   <!-- optional profile to use javasvn instead of the SVN CLI for the buildNumber plugin -->
   <profile>
     <id>javasvn</id>
     <build>
       <plugins>
         <plugin>
           <groupId>org.codehaus.mojo</groupId>
           <artifactId>buildnumber-maven-plugin</artifactId>
           <configuration>
             <providerImplementations>
               <svn>javasvn</svn>
             </providerImplementations>
           </configuration>
         </plugin>
       </plugins>
     </build>
   </profile>

    <!-- Optional Clover profile -->
    <profile>
      <id>clover</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.atlassian.maven.plugins</groupId>
            <artifactId>maven-clover2-plugin</artifactId>
            <version>${hc.clover2.version}</version>
            <configuration>
              <flushPolicy>threaded</flushPolicy>
              <flushInterval>100</flushInterval>
              <targetPercentage>50%</targetPercentage>
            </configuration>
            <executions>
              <execution>
                <id>site</id>
                <phase>pre-site</phase>
                <goals>
                  <goal>instrument</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>

      <reporting>
        <plugins>
          <plugin>
            <groupId>com.atlassian.maven.plugins</groupId>
            <artifactId>maven-clover2-plugin</artifactId>
            <version>${hc.clover2.version}</version>
            <configuration>
              <jdk>1.5</jdk>
            </configuration>
          </plugin>
        </plugins>
      </reporting>

    </profile>

 </profiles>

 <prerequisites>
   <maven>3.0.3</maven>
 </prerequisites>

 <properties>
   <maven.compiler.source>1.5</maven.compiler.source>
   <maven.compiler.target>1.5</maven.compiler.target>
   <maven.compiler.optimize>true</maven.compiler.optimize>
   <maven.compiler.showDeprecation>true</maven.compiler.showDeprecation>
   <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
   <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
   <hc.site.url>scp://people.apache.org/www/hc.apache.org/</hc.site.url>

   <!-- Define versions of all report plugins, because they should match usage in pluginManagement and modules -->
   <hc.clirr.version>2.5</hc.clirr.version>
   <hc.clover2.version>3.1.11</hc.clover2.version>
   <hc.javadoc.version>2.9.1</hc.javadoc.version>
   <hc.jxr.version>2.3</hc.jxr.version>
   <hc.surefire-report.version>2.15</hc.surefire-report.version>
   <hc.surefire.version>2.15</hc.surefire.version>
   <hc.project-info.version>2.7</hc.project-info.version>

   <!-- build meta inf -->
   <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ssZ</maven.build.timestamp.format>
   <implementation.build>${scmBranch}@r${buildNumber}; ${maven.build.timestamp}</implementation.build>

   <!-- Allow Clirr severity to be overriden by the command-line option -DminSeverity=level -->
   <minSeverity>info</minSeverity>
 </properties>
</project>

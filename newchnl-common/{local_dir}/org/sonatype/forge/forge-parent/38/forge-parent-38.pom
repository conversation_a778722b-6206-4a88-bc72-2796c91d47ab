<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2008-2013 Sonatype, Inc. All rights reserved.

    This program is licensed to you under the Apache License Version 2.0,
    and you may not use this file except in compliance with the Apache License Version 2.0.
    You may obtain a copy of the Apache License Version 2.0 at http://www.apache.org/licenses/LICENSE-2.0.

    Unless required by applicable law or agreed to in writing,
    software distributed under the Apache License Version 2.0 is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the Apache License Version 2.0 for the specific language governing permissions and limitations there under.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.sonatype.forge</groupId>
  <artifactId>forge-parent</artifactId>
  <packaging>pom</packaging>
  <version>38</version>

  <name>${project.artifactId}</name>
  <description>${project.name}</description>
  <inceptionYear>2008</inceptionYear>
  <url>http://forge.sonatype.com/</url>

  <organization>
    <name>Sonatype, Inc.</name>
    <url>http://www.sonatype.com/</url>
  </organization>

  <licenses>
    <license>
      <name>ASLv2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <name>Sonatype, Inc.</name>
      <organization>Sonatype, Inc.</organization>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:git://github.com/sonatype/oss-parents.git</connection>
    <url>https://github.com/sonatype/oss-parents</url>
    <developerConnection>scm:git:**************:sonatype/oss-parents.git</developerConnection>
  </scm>

  <ciManagement>
    <system>Bamboo</system>
    <url>https://bamboo.zion.sonatype.com</url>
  </ciManagement>

  <issueManagement>
    <system>Jira</system>
    <url>https://issues.sonatype.org</url>
  </issueManagement>

  <prerequisites>
    <maven>3.0</maven>
  </prerequisites>

  <properties>
    <nexusUrl>https://repository.sonatype.org/</nexusUrl>
    <forgeReleaseId>forge-releases</forgeReleaseId>
    <forgeReleaseUrl>https://repository.sonatype.org/service/local/staging/deploy/maven2</forgeReleaseUrl>
    <forgeSnapshotId>forge-snapshots</forgeSnapshotId>
    <forgeSnapshotUrl>https://repository.sonatype.org/content/repositories/snapshots</forgeSnapshotUrl>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
    <surefire.failIfNoSpecifiedTests>false</surefire.failIfNoSpecifiedTests>
    <localCheckout>true</localCheckout>
    <pushChanges>false</pushChanges>
    <failsafe.timeout>600</failsafe.timeout>
    <nexus-staging.version>1.4.4</nexus-staging.version>
    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>
  </properties>

  <distributionManagement>
    <repository>
      <id>${forgeReleaseId}</id>
      <url>${forgeReleaseUrl}</url>
    </repository>
    <snapshotRepository>
      <id>${forgeSnapshotId}</id>
      <url>${forgeSnapshotUrl}</url>
    </snapshotRepository>
  </distributionManagement>

  <build>

    <defaultGoal>install</defaultGoal>

    <resources>
      <resource>
        <directory>${project.basedir}/src/main/resources</directory>
        <filtering>false</filtering>
        <includes>
          <include>**/*</include>
        </includes>
      </resource>

      <resource>
        <directory>${project.basedir}/src/main/filtered-resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>**/*</include>
        </includes>
      </resource>
    </resources>

    <testResources>
      <testResource>
        <directory>${project.basedir}/src/test/resources</directory>
        <filtering>false</filtering>
        <includes>
          <include>**/*</include>
        </includes>
      </testResource>

      <testResource>
        <directory>${project.basedir}/src/test/filtered-resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>**/*</include>
        </includes>
      </testResource>
    </testResources>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.9</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>1.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.4</version>
          <configuration>
            <tarLongFileMode>gnu</tarLongFileMode>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.3.7</version>
          <configuration>
            <instructions>
              <!-- avoid WARNING No translation found for macro: project.name since we default to ${project.name} -->
              <Bundle-Description>${project.description}</Bundle-Description>
            </instructions>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.0</version>
          <configuration>
            <source>${maven.compiler.source}</source>
            <target>${maven.compiler.target}</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.5.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>1.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>2.13</version>
          <configuration>
            <redirectTestOutputToFile>${maven.test.redirectTestOutputToFile}</redirectTestOutputToFile>
            <forkMode>always</forkMode>
            <forkedProcessTimeoutInSeconds>${failsafe.timeout}</forkedProcessTimeoutInSeconds>
            <systemPropertyVariables>
              <java.awt.headless>true</java.awt.headless>
              <!-- SUREFIRE-764 workaround force forked executions to inherit -Djava.io.tmpdir=... -->
              <java.io.tmpdir>${java.io.tmpdir}</java.io.tmpdir>
            </systemPropertyVariables>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>1.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.4</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.9</version>
        </plugin>
        <!-- mvn license:format -N, add to settings <pluginGroup>com.mycila.maven-license-plugin</pluginGroup> -->
        <plugin>
          <groupId>com.mycila.maven-license-plugin</groupId>
          <artifactId>maven-license-plugin</artifactId>
          <version>1.9.0</version>
          <configuration>
            <encoding>UTF-8</encoding>
            <strictCheck>true</strictCheck>
            <useDefaultExcludes>false</useDefaultExcludes>
            <header>${project.basedir}/header.txt</header>
            <includes>
              <include>**/pom.xml</include>
              <include>**/*.xml</include>
              <include>**/*.xsd</include>
              <include>**/*.xjb</include>
              <include>**/*.properties</include>
              <include>**/*.ini</include>
              <include>**/*.java</include>
              <include>**/*.groovy</include>
              <include>**/*.scala</include>
              <include>**/*.aj</include>
              <include>**/*.js</include>
              <include>**/*.css</include>
              <include>**/*.help</include>
              <include>**/*.proto</include>
              <include>**/*.sm</include>
              <include>**/*.bat</include>
              <include>**/*.xsl</include>
              <include>**/*.html</include>
              <include>**/*.vm</include>
              <include>**/*.md</include>
            </includes>
            <excludes>
              <exclude>**/target/**</exclude>
              <exclude>**/.*/**</exclude>
              <exclude>**/dependency-reduced-pom.xml</exclude>
              <exclude>**/nbactions*.xml</exclude>
              <exclude>**/nb-configuration.xml</exclude>
              <exclude>**/atlassian-ide-plugin.xml</exclude>
              <exclude>**/release.properties</exclude>
              <exclude>**/META-INF/services/**</exclude>
            </excludes>
            <mapping>
              <aj>JAVADOC_STYLE</aj>
              <groovy>JAVADOC_STYLE</groovy>
              <help>SCRIPT_STYLE</help>
              <ini>SCRIPT_STYLE</ini>
              <java>SLASHSTAR_STYLE</java>
              <md>XML_STYLE</md>
              <proto>DOUBLESLASH_STYLE</proto>
              <scala>SLASHSTAR_STYLE</scala>
              <sm>DOUBLESLASH_STYLE</sm>
              <vm>SHARPSTAR_STYLE</vm>
              <xjb>XML_STYLE</xjb>
            </mapping>
            <aggregate>true</aggregate>
            <goals>
              <goal>check</goal>
            </goals>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>3.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <goals>deploy</goals>
            <!-- pushChanges and localCheckout for dvcs and require a 'git push origin \-\-tags :' after release:perform -->
            <localCheckout>${localCheckout}</localCheckout>
            <pushChanges>${pushChanges}</pushChanges>
            <!--  forked path prevents hang see MGPG-9 -->
            <mavenExecutorId>forked-path</mavenExecutorId>
            <releaseProfiles>release</releaseProfiles>
            <useReleaseProfile>false</useReleaseProfile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-repository-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-plugin</artifactId>
          <version>1.8.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>${nexus-staging.version}</version>
          <extensions>true</extensions>
          <configuration>
            <nexusUrl>${nexusUrl}</nexusUrl>
            <keepStagingRepositoryOnCloseRuleFailure>true</keepStagingRepositoryOnCloseRuleFailure>
            <tags>
              <localUsername>${env.USER}</localUsername>
              <javaVersion>${java.version}</javaVersion>
            </tags>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.13</version>
          <configuration>
            <redirectTestOutputToFile>${maven.test.redirectTestOutputToFile}</redirectTestOutputToFile>
            <!-- make friendly with -Dtest=skip since there is no -DskipUnits like there is -DskipITs -->
            <failIfNoSpecifiedTests>${surefire.failIfNoSpecifiedTests}</failIfNoSpecifiedTests>
            <systemPropertyVariables>
              <java.awt.headless>true</java.awt.headless>
              <!-- SUREFIRE-764 workaround force forked executions to inherit -Djava.io.tmpdir=... -->
              <java.io.tmpdir>${java.io.tmpdir}</java.io.tmpdir>
            </systemPropertyVariables>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>1.6</version>
          <configuration>
            <useJava5>true</useJava5>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>sisu-maven-plugin</artifactId>
          <version>1.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <configuration>
          <serverId>${forgeSnapshotId}</serverId>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
            <configuration>
              <includePom>true</includePom>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- deploy plugin is left in case some project would like to use it instead of staging plugin -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <!-- this presumes the correct gpg.passphrase property in the settings "release" profile -->
              <passphrase>${gpg.passphrase}</passphrase>
              <!-- most likely we are using an agent -->
              <useAgent>true</useAgent>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <configuration>
              <!-- FIXME as of 1.2 a bug in deferred deploy requires url to be explicitly set instead of reaping it from distMgt -->
              <serverId>${forgeReleaseId}</serverId>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.sisu.inject</groupId>
    <artifactId>guice-parent</artifactId>
    <version>3.2.3</version>
  </parent>

  <groupId>org.sonatype.sisu</groupId>
  <artifactId>sisu-guice</artifactId>

  <name>Sisu Guice - Core Library</name>

  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.6.4</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
    </dependency>
    <dependency>
      <groupId>aopalliance</groupId>
      <artifactId>aopalliance</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <!--
     | CGLIB is embedded by default by the JarJar build profile
    -->
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
      <optional>true</optional>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>cglib</groupId>
      <artifactId>cglib</artifactId>
      <optional>true</optional>
      <scope>provided</scope>
    </dependency>
    <!--
     | Test dependencies
    -->
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject-tck</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava-testlib</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>3.0.5.RELEASE</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>biz.aQute</groupId>
      <artifactId>bnd</artifactId>
      <version>0.0.384</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.apache.felix.framework</artifactId>
      <version>3.0.5</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!--
       | Add standard LICENSE and NOTICE files
      -->
      <plugin>
        <artifactId>maven-remote-resources-plugin</artifactId>
      </plugin>
      <!--
       | Enable Java6 conformance checks
      -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!--
           | Run core tests without SLF4J on the classpath
          -->
          <classpathDependencyExcludes>
            <exclude>org.slf4j:slf4j-api</exclude>
          </classpathDependencyExcludes>
          <!--
           | Temporarily excluded tests
          -->
          <excludes>
            <exclude>**/*$*</exclude>
            <exclude>**/ErrorHandlingTest*</exclude>
            <exclude>**/OSGiContainerTest*</exclude>
            <exclude>**/ScopesTest*</exclude>
            <exclude>**/TypeConversionTest*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <!--
       | Add OSGi manifest
      -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <instructions>
            <Bundle-Name>${project.artifactId}$(if;$(classes;NAMED;*.MethodAspect);; (no_aop))</Bundle-Name>
            <Import-Package>!net.sf.cglib.*,!org.objectweb.asm.*,!com.google.inject.*,*</Import-Package>
            <Eclipse-ExtensibleAPI>true</Eclipse-ExtensibleAPI>
            <DynamicImport-Package>org.slf4j</DynamicImport-Package>
          </instructions>
        </configuration>
      </plugin>
      <!--
       | Remove duplicate jarjar'd LICENSE and NOTICE
      -->
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>LICENSE</exclude>
            <exclude>NOTICE</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <!--
       | No-AOP profile: repeat the build lifecycle with munged no-AOP source
      -->
      <id>guice.with.no_aop</id>
      <activation>
        <property>
          <name>guice.with.no_aop</name>
          <value>!false</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>munge-maven-plugin</artifactId>
            <version>1.0</version>
            <executions>
              <execution>
                <phase>prepare-package</phase>
                <goals>
                  <goal>munge-fork</goal>
                </goals>
                <configuration>
                  <symbols>NO_AOP</symbols>
                  <excludes>
                    **/InterceptorBinding.java,
                    **/InterceptorBindingProcessor.java,
                    **/InterceptorStackCallback.java,
                    **/LineNumbers.java,
                    **/MethodAspect.java,
                    **/ProxyFactory.java,
                    **/BytecodeGenTest.java,
                    **/IntegrationTest.java,
                    **/MethodInterceptionTest.java,
                    **/ProxyFactoryTest.java
                  </excludes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!--
           | Package the no-AOP build with its own OSGi manifest and attach using "no_aop" classifier
          -->
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <id>no_aop</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classesDirectory>${project.build.directory}/munged/classes</classesDirectory>
                  <classifier>no_aop</classifier>
                  <archive>
                    <manifestFile>${project.build.directory}/munged/classes/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <!--
       | JarJar build profile: Embed CGLIB (and ASM) classes under a Guice namespace
      -->
      <id>guice.with.jarjar</id>
      <activation>
        <property>
          <name>guice.with.jarjar</name>
          <value>!false</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>jarjar-maven-plugin</artifactId>
            <version>1.9</version>
            <executions>
              <execution>
                <id>jarjar</id>
                <goals><goal>jarjar</goal></goals>
              </execution>
            </executions>
            <configuration>
              <overwrite>true</overwrite>
              <includes>
                <include>*:asm*</include>
                <include>*:cglib</include>
              </includes>
              <rules>
                <rule>
                  <pattern>net.sf.cglib.*</pattern>
                  <result>com.google.inject.internal.cglib.$@1</result>
                </rule>
                <rule>
                  <pattern>net.sf.cglib.**.*</pattern>
                  <result>com.google.inject.internal.cglib.@1.$@2</result>
                </rule>
                <rule>
                  <pattern>org.objectweb.asm.*</pattern>
                  <result>com.google.inject.internal.asm.$@1</result>
                </rule>
                <rule>
                  <pattern>org.objectweb.asm.**.*</pattern>
                  <result>com.google.inject.internal.asm.@1.$@2</result>
                </rule>
                <keep>
                  <pattern>com.google.inject.**</pattern>
                </keep>
                <keep>
                  <pattern>com.googlecode.**</pattern>
                </keep>
              </rules>
            </configuration>
          </plugin>
          <plugin>
            <!--
             | Attach the original non-JarJar'd classes so extensions can compile against them
            -->
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <version>1.8</version>
            <executions>
              <execution>
                <id>classes</id>
                <phase>package</phase>
                <goals>
                  <goal>attach-artifact</goal>
                </goals>
                <configuration>
                  <artifacts>
                    <artifact>
                      <file>${project.build.directory}/original-${project.build.finalName}.jar</file>
                      <classifier>classes</classifier>
                    </artifact>
                  </artifacts>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <!--
       | m2e profile - enable use of JarJar inside Eclipse
      -->
      <id>m2e</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.eclipse.m2e</groupId>
              <artifactId>lifecycle-mapping</artifactId>
              <version>1.0.0</version>
              <configuration>
                <lifecycleMappingMetadata>
                  <pluginExecutions>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>jarjar-maven-plugin</artifactId>
                        <versionRange>[1.4,)</versionRange>
                        <goals><goal>jarjar</goal></goals>
                      </pluginExecutionFilter>
                      <action><execute /></action>
                    </pluginExecution>
                  </pluginExecutions>
                </lifecycleMappingMetadata>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>

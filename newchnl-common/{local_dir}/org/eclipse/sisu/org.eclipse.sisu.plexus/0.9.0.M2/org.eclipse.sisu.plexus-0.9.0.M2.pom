<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010-present Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~ http://www.eclipse.org/legal/epl-v10.html
 ~
 ~ Contributors:
 ~   <PERSON> (Sonatype, Inc.) - initial API and implementation
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.eclipse.sisu</groupId>
    <artifactId>sisu-plexus</artifactId>
    <version>0.9.0.M2</version>
  </parent>

  <artifactId>org.eclipse.sisu.plexus</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <!--
     | Sisu is compatible with Guice 3.0+
    -->
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>4.0</version>
      <scope>provided</scope>
    </dependency>
    <!--
     | @PostConstruct and @PreDestroy help with Plexus->JSR330 migration
    -->
    <dependency>
      <groupId>javax.annotation</groupId>
      <artifactId>javax.annotation-api</artifactId>
      <version>1.2</version>
    </dependency>
    <!--
     | CDI's @Typed helps with Plexus->JSR330 migration
    -->
    <dependency>
      <groupId>javax.enterprise</groupId>
      <artifactId>cdi-api</artifactId>
      <version>1.2</version>
      <exclusions>
        <exclusion>
          <groupId>javax.el</groupId>
          <artifactId>javax.el-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.interceptor</groupId>
          <artifactId>javax.interceptor-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.sisu</groupId>
      <artifactId>org.eclipse.sisu.inject</artifactId>
      <version>0.9.0.M2</version>
    </dependency>
    <!--
     | Plexus classloading/utilities
    -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
      <version>2.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-classworlds</artifactId>
      <version>2.6.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.3.0</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>osgi.core</artifactId>
      <version>5.0.0</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.1.11</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>.</directory>
        <includes>
          <include>META-INF/**</include>
          <include>about.html</include>
          <include>overview.html</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <stripVersion>true</stripVersion>
        </configuration>
        <executions>
          <execution>
            <id>guice3</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <classifier>no_aop</classifier>
                  <version>3.0</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice3</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>guice4</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
                  <version>16.0.1</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <classifier>no_aop</classifier>
                  <version>4.0</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice4</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>guice423</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>failureaccess</artifactId>
                  <version>1.0.1</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
                  <version>27.1-jre</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <classifier>no_aop</classifier>
                  <version>4.2.3</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice423</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>guice5</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>failureaccess</artifactId>
                  <version>1.0.1</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
                  <version>30.1-jre</version>
                </artifactItem>
                <artifactItem>
                  <groupId>org.ow2.asm</groupId>
                  <artifactId>asm</artifactId>
                  <version>9.5</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <classifier>classes</classifier>
                  <version>5.1.0</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice5</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>copy-old-annotations</id>
            <phase>process-resources</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <stripVersion>false</stripVersion>
              <artifactItems>
                <artifactItem>
                  <groupId>org.codehaus.plexus</groupId>
                  <artifactId>plexus-component-annotations</artifactId>
                  <version>1.2.1</version>
                </artifactItem>
              </artifactItems>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <redirectTestOutputToFile>true</redirectTestOutputToFile>
          <argLine>-Xmx64m @{jacoco.argLine}</argLine>
          <classpathDependencyExcludes>
            <classpathDependencyExclude>com.google.guava:guava</classpathDependencyExclude>
            <classpathDependencyExclude>com.google.inject:guice</classpathDependencyExclude>
          </classpathDependencyExcludes>
        </configuration>
        <executions>
          <execution>
            <id>default-test</id>
            <configuration>
              <skip>true</skip>
            </configuration>
          </execution>
          <execution>
            <id>base</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <groups>org.eclipse.sisu.BaseTests</groups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice3/guice-no_aop.jar</additionalClasspathElement>
              </additionalClasspathElements>
            </configuration>
          </execution>
          <execution>
            <id>guice3</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice3</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice3/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice3/guice-no_aop.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice3</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
          <execution>
            <id>guice4</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice4</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice4/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/guice-no_aop.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice4</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
          <execution>
            <id>guice423</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice423</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice423/failureaccess.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice423/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice423/guice-no_aop.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice423</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
          <execution>
            <id>guice5</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice5</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice5/failureaccess.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/asm.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/guice-classes.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice5</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-clean-plugin</artifactId>
        <executions>
          <execution>
            <id>remove-missing-class</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>clean</goal>
            </goals>
            <configuration>
              <excludeDefaultDirectories>true</excludeDefaultDirectories>
              <filesets>
                <fileset>
                  <directory>${project.build.testOutputDirectory}</directory>
                  <includes>
                    <include>**/*MissingClass.class</include>
                  </includes>
                </fileset>
              </filesets>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <propertyName>jacoco.argLine</propertyName>
        </configuration>
        <executions>
          <execution>
            <id>jacoco-prepare</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>jacoco-report</id>
            <phase>verify</phase>
            <goals>
              <goal>report-aggregate</goal>
            </goals>
            <configuration>
              <title>Sisu Plexus</title>
              <outputDirectory>${project.basedir}/../target/site/jacoco</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>

<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010-present Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~ http://www.eclipse.org/legal/epl-v10.html
 ~
 ~ Contributors:
 ~   <PERSON> (Sonatype, Inc.) - initial API and implementation
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.eclipse.sisu</groupId>
  <artifactId>sisu-inject</artifactId>
  <version>0.9.0.M2</version>
  <packaging>pom</packaging>

  <name>Sisu Inject</name>
  <description>JSR330-based container; supports classpath scanning, auto-binding, and dynamic auto-wiring</description>
  <url>http://www.eclipse.org/sisu/</url>
  <inceptionYear>2010</inceptionYear>
  <organization>
    <name>The Eclipse Foundation</name>
    <url>http://www.eclipse.org/</url>
  </organization>
  <licenses>
    <license>
      <name>Eclipse Public License, Version 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <mailingLists>
    <mailingList>
      <name>Sisu Developers List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://dev.eclipse.org/mhonarc/lists/sisu-dev/</archive>
    </mailingList>
    <mailingList>
      <name>Sisu Users List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://dev.eclipse.org/mhonarc/lists/sisu-users/</archive>
    </mailingList>
  </mailingLists>

  <modules>
    <module>org.eclipse.sisu.inject</module>
    <module>org.eclipse.sisu.inject.extender</module>
  </modules>

  <developers>
    <developer>
      <id>mcculls</id>
      <name>Stuart McCulloch</name>
    </developer>
    <developer>
      <id>cstamas</id>
      <name>Tamas Cservenak</name>
    </developer>
    <developer>
      <id>kwin</id>
      <name>Konrad Windszus</name>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:git://github.com/eclipse/sisu.inject.git</connection>
    <developerConnection>scm:git:**************:eclipse/sisu.inject.git</developerConnection>
    <url>https://github.com/eclipse/sisu.inject</url>
    <tag>milestones/0.9.0.M2</tag>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/eclipse/sisu.inject/issues</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci.eclipse.org/sisu/job/sisu.inject/</url>
  </ciManagement>

  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <properties>
    <maven.compiler.release>8</maven.compiler.release>
    <!-- Set to same version as release target for consistency -->
    <maven.compiler.source>1.${maven.compiler.release}</maven.compiler.source>
    <maven.compiler.target>1.${maven.compiler.release}</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <mavenBuildVersion>3.6.3</mavenBuildVersion>
    <javaBuildVersion>11</javaBuildVersion>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.2.1</version>
          <configuration>
            <rules>
              <enforceBytecodeVersion>
                <maxJdkVersion>${maven.compiler.target}</maxJdkVersion>
                <excludes>
                  <!-- Used in tests only -->
                  <exclude>org.junit.jupiter:junit-jupiter-api</exclude>
                  <exclude>org.junit.platform:junit-platform-commons</exclude>
                  <exclude>org.apache.felix:org.apache.felix.framework:jar:7.0.5</exclude>
                </excludes>
              </enforceBytecodeVersion>
              <requireMavenVersion>
                <version>${mavenBuildVersion}</version>
              </requireMavenVersion>
              <requireJavaVersion>
                <version>${javaBuildVersion}</version>
              </requireJavaVersion>
            </rules>
          </configuration>
          <executions>
            <execution>
              <id>enforce-versions</id>
              <goals>
                <goal>enforce</goal>
              </goals>
            </execution>
          </executions>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>extra-enforcer-rules</artifactId>
              <version>1.6.1</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.2.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.11.0</version>
          <configuration>
            <proc>none</proc>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.5.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>3.0.0</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>3.0.0</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>3.0.0</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>biz.aQute.bnd</groupId>
          <artifactId>bnd-maven-plugin</artifactId>
          <version>6.4.0</version>
          <configuration>
            <bnd><![CDATA[
Bundle-Copyright: Copyright (c) 2010-present Sonatype, Inc. and others
Bundle-DocURL: http://www.eclipse.org/sisu/
-noextraheaders: true
-snapshot: SNAPSHOT
            ]]></bnd>
          </configuration>
          <executions>
            <execution>
              <id>bnd-process</id>
              <goals>
                <goal>bnd-process</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.3.0</version>
          <executions>
            <execution>
              <id>default-jar</id>
              <configuration>
                <archive>
                  <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                </archive>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>3.4.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>0.8.8</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.0</version>
          <configuration>
            <mavenExecutorId>forked-path</mavenExecutorId>
            <useReleaseProfile>false</useReleaseProfile>
            <arguments>-Psonatype-oss-release</arguments>
            <localCheckout>true</localCheckout>
            <pushChanges>false</pushChanges>
            <scmCommentPrefix>|</scmCommentPrefix>
            <signTag>true</signTag>
            <tagNameFormat>milestones/@{project.version}</tagNameFormat>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.5.0</version>
          <configuration>
            <overview>${basedir}/overview.html</overview>
            <excludePackageNames>*.internal,*.asm</excludePackageNames>
            <additionalJOption>-Xdoclint:all,-missing</additionalJOption>
            <detectOfflineLinks>false</detectOfflineLinks>
            <quiet>true</quiet>
          </configuration>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>3.0.1</version>
          <configuration>
            <passphrase>${gpg.passphrase}</passphrase>
            <useAgent>true</useAgent>
          </configuration>
          <executions>
            <execution>
              <id>sign-artifacts</id>
              <phase>verify</phase>
              <goals>
                <goal>sign</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.13</version>
          <extensions>true</extensions>
          <configuration>
            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
            <serverId>sonatype-nexus-staging</serverId>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.4.2</version>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>2.35.0</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>spotless</id>
      <activation>
        <file>
          <exists>${basedir}/../sisu-eclipse-codestyle.xml</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>com.diffplug.spotless</groupId>
            <artifactId>spotless-maven-plugin</artifactId>
            <configuration>
              <java>
                <eclipse>
                  <file>${basedir}/../sisu-eclipse-codestyle.xml</file>
                </eclipse>
                <includes>
                  <include>src/**/*.java</include>
                </includes>
                <excludes>
                  <exclude>src/**/asm/*.java</exclude>
                </excludes>
              </java>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>sonatype-oss-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <extensions>true</extensions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010-present Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~ http://www.eclipse.org/legal/epl-v10.html
 ~
 ~ Contributors:
 ~   <PERSON> (Sonatype, Inc.) - initial API and implementation
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.eclipse.sisu</groupId>
    <artifactId>sisu-inject</artifactId>
    <version>0.9.0.M2</version>
  </parent>

  <artifactId>org.eclipse.sisu.inject</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <!--
     | Sisu is compatible with Guice 3.0+
    -->
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
      <version>4.0</version>
      <scope>provided</scope>
    </dependency>

    <!--
     | Optional support dependencies
    -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>osgi.core</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.annotation</groupId>
      <artifactId>javax.annotation-api</artifactId>
      <version>1.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.enterprise</groupId>
      <artifactId>cdi-api</artifactId>
      <version>1.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
      <version>2.5</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.google.inject.extensions</groupId>
      <artifactId>guice-servlet</artifactId>
      <version>4.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.3.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <version>6.7</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.1.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.inject.extensions</groupId>
      <artifactId>guice-assistedinject</artifactId>
      <version>4.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.apache.felix.framework</artifactId>
      <version>7.0.5</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>.</directory>
        <includes>
          <include>META-INF/**</include>
          <include>about.html</include>
          <include>overview.html</include>
          <include>plugin.xml</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <stripVersion>true</stripVersion>
        </configuration>
        <executions>
          <execution>
            <id>guice3</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <version>3.0</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject.extensions</groupId>
                  <artifactId>guice-assistedinject</artifactId>
                  <version>3.0</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject.extensions</groupId>
                  <artifactId>guice-servlet</artifactId>
                  <version>3.0</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice3</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>guice4</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>failureaccess</artifactId>
                  <version>1.0.1</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
                  <version>27.1-jre</version>
                </artifactItem>
                <artifactItem>
                  <groupId>org.ow2.asm</groupId>
                  <artifactId>asm</artifactId>
                  <version>9.5</version>
                </artifactItem>
                <artifactItem>
                  <groupId>cglib</groupId>
                  <artifactId>cglib</artifactId>
                  <version>3.3.0</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <classifier>classes</classifier>
                  <version>4.2.3</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject.extensions</groupId>
                  <artifactId>guice-assistedinject</artifactId>
                  <version>4.2.3</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject.extensions</groupId>
                  <artifactId>guice-servlet</artifactId>
                  <version>4.2.3</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice4</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>guice5</id>
            <phase>test</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>failureaccess</artifactId>
                  <version>1.0.1</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
                  <version>30.1-jre</version>
                </artifactItem>
                <artifactItem>
                  <groupId>org.ow2.asm</groupId>
                  <artifactId>asm</artifactId>
                  <version>9.5</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject</groupId>
                  <artifactId>guice</artifactId>
                  <classifier>classes</classifier>
                  <version>5.1.0</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject.extensions</groupId>
                  <artifactId>guice-assistedinject</artifactId>
                  <version>5.1.0</version>
                </artifactItem>
                <artifactItem>
                  <groupId>com.google.inject.extensions</groupId>
                  <artifactId>guice-servlet</artifactId>
                  <version>5.1.0</version>
                </artifactItem>
              </artifactItems>
              <outputDirectory>${project.build.directory}/guice5</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <redirectTestOutputToFile>true</redirectTestOutputToFile>
          <argLine>-Xmx64m @{jacoco.argLine}</argLine>
          <classpathDependencyExcludes>
            <classpathDependencyExclude>com.google.guava:guava</classpathDependencyExclude>
            <classpathDependencyExclude>com.google.inject:guice</classpathDependencyExclude>
            <classpathDependencyExclude>com.google.inject.extensions:guice-assistedinject</classpathDependencyExclude>
            <classpathDependencyExclude>com.google.inject.extensions:guice-servlet</classpathDependencyExclude>
            <classpathDependencyExclude>org.osgi:osgi.core</classpathDependencyExclude>
          </classpathDependencyExcludes>
        </configuration>
        <executions>
          <execution>
            <id>default-test</id>
            <configuration>
              <skip>true</skip>
            </configuration>
          </execution>
          <execution>
            <id>base</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <groups>org.eclipse.sisu.BaseTests</groups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice3/guice.jar</additionalClasspathElement>
              </additionalClasspathElements>
            </configuration>
          </execution>
          <execution>
            <id>guice3</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice3</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice3/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice3/guice.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice3/guice-assistedinject.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice3/guice-servlet.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice3</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
          <execution>
            <id>guice4</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice4</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice4/failureaccess.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/asm.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/cglib.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/guice-classes.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/guice-assistedinject.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice4/guice-servlet.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice4</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
          <execution>
            <id>guice5</id>
            <goals>
              <goal>test</goal>
            </goals>
            <configuration>
              <reportNameSuffix>guice5</reportNameSuffix>
              <excludedGroups>org.eclipse.sisu.BaseTests</excludedGroups>
              <additionalClasspathElements>
                <additionalClasspathElement>${project.build.directory}/guice5/failureaccess.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/guava.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/asm.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/guice-classes.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/guice-assistedinject.jar</additionalClasspathElement>
                <additionalClasspathElement>${project.build.directory}/guice5/guice-servlet.jar</additionalClasspathElement>
              </additionalClasspathElements>
              <systemPropertyVariables>
                <guiceVersion>guice5</guiceVersion>
              </systemPropertyVariables>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <propertyName>jacoco.argLine</propertyName>
          <excludes>
            <exclude>Incomplete*</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <id>jacoco-prepare</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>jacoco-report</id>
            <goals>
              <goal>report</goal>
            </goals>
            <configuration>
              <title>Sisu Inject</title>
              <outputDirectory>${project.basedir}/../target/site/jacoco</outputDirectory>
              <excludes>
                <exclude>**/asm/*</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>sonatype-oss-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-shade-plugin</artifactId>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>shade</goal>
                </goals>
                <configuration>
                  <!--
                   | Create 'no_asm' jar which doesn't embed ASM and refers to the original org.objectweb.asm package.
                  -->
                  <shadedArtifactAttached>true</shadedArtifactAttached>
                  <shadedClassifierName>no_asm</shadedClassifierName>
                  <filters>
                    <filter>
                      <artifact>org.eclipse.sisu:org.eclipse.sisu.inject</artifact>
                      <excludes>
                        <exclude>org/eclipse/sisu/space/asm/**</exclude>
                      </excludes>
                    </filter>
                  </filters>
                  <relocations>
                    <relocation>
                      <pattern>org.eclipse.sisu.space.asm</pattern>
                      <shadedPattern>org.objectweb.asm</shadedPattern>
                    </relocation>
                  </relocations>
                  <transformers>
                    <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                      <manifestEntries>
                        <DynamicImport-Package>org.objectweb.asm</DynamicImport-Package>
                      </manifestEntries>
                    </transformer>
                  </transformers>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010, 2014 Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~ http://www.eclipse.org/legal/epl-v10.html
 ~
 ~ Contributors:
 ~    Sonatype, Inc. - initial API and implementation
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.eclipse.aether</groupId>
  <artifactId>aether</artifactId>
  <version>1.0.0.v20140518</version>
  <packaging>pom</packaging>

  <name>Aether</name>
  <description>
    The parent and aggregator for the repository system.
  </description>
  <url>http://www.eclipse.org/aether/</url>
  <inceptionYear>2010</inceptionYear>

  <organization>
    <name>The Eclipse Foundation</name>
    <url>http://www.eclipse.org/</url>
  </organization>

  <mailingLists>
    <mailingList>
      <name>Aether Developer List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/aether-dev</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/aether-dev</unsubscribe>
      <post><EMAIL></post>
      <archive>http://dev.eclipse.org/mhonarc/lists/aether-dev/</archive>
    </mailingList>
    <mailingList>
      <name>Aether User List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/aether-users</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/aether-users</unsubscribe>
      <post><EMAIL></post>
      <archive>http://dev.eclipse.org/mhonarc/lists/aether-users/</archive>
    </mailingList>
    <mailingList>
      <name>Aether Commit Notification List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/aether-commit</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/aether-commit</unsubscribe>
      <archive>http://dev.eclipse.org/mhonarc/lists/aether-commit/</archive>
    </mailingList>
    <mailingList>
      <name>Aether CI Notification List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/aether-build</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/aether-build</unsubscribe>
      <archive>http://dev.eclipse.org/mhonarc/lists/aether-build/</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:git://git.eclipse.org/gitroot/aether/aether-core.git</connection>
    <developerConnection>scm:git:ssh://git.eclipse.org/gitroot/aether/aether-core.git</developerConnection>
    <url>http://git.eclipse.org/c/aether/aether-core.git/tree/</url>
  </scm>

  <issueManagement>
    <system>bugzilla</system>
    <url>https://bugs.eclipse.org/bugs/buglist.cgi?query_format=specific&amp;bug_status=__open__&amp;product=Aether</url>
  </issueManagement>

  <ciManagement>
    <system>Hudson</system>
    <url>https://hudson.eclipse.org/aether/job/aether-core/</url>
  </ciManagement>

  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>${sonatypeOssDistMgmtSnapshotsId}</id>
      <url>${sonatypeOssDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
  </distributionManagement>

  <licenses>
    <license>
      <name>Eclipse Public License, Version 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>bbentmann</id>
      <name>Benjamin Bentmann</name>
      <roles>
        <role>Project Lead</role>
      </roles>
    </developer>
    <developer>
      <id>jvanzyl</id>
      <name>Jason Van Zyl</name>
      <roles>
        <role>Project Lead</role>
      </roles>
    </developer>
  </developers>

  <modules>
    <!-- NOTE: Be sure to update the bin assembly descriptor as well if the module list changes -->
    <module>aether-api</module>
    <module>aether-spi</module>
    <module>aether-util</module>
    <module>aether-impl</module>
    <module>aether-test-util</module>
    <module>aether-connector-basic</module>
    <module>aether-transport-classpath</module>
    <module>aether-transport-file</module>
    <module>aether-transport-http</module>
    <module>aether-transport-wagon</module>
  </modules>

  <properties>
    <bundle.env>J2SE-1.5</bundle.env>
    <bundle.vendor>Eclipse Aether</bundle.vendor>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <surefire.redirectTestOutputToFile>true</surefire.redirectTestOutputToFile>
    <sonatypeOssDistMgmtSnapshotsId>sonatype-nexus-snapshots</sonatypeOssDistMgmtSnapshotsId>
    <sonatypeOssDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/snapshots/</sonatypeOssDistMgmtSnapshotsUrl>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-util</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-connector-basic</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.aether</groupId>
        <artifactId>aether-test-util</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.11</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>1.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>1.3</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>1.5.5</version>
        <scope>provided</scope>
      </dependency>

      <dependency>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>org.eclipse.sisu.inject</artifactId>
        <version>0.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>org.eclipse.sisu.plexus</artifactId>
        <version>0.1.1</version>
        <exclusions>
          <exclusion>
            <groupId>javax.enterprise</groupId>
            <artifactId>cdi-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-guice</artifactId>
        <version>3.1.6</version>
        <classifier>no_aop</classifier>
        <exclusions>
          <exclusion>
            <groupId>aopalliance</groupId>
            <artifactId>aopalliance</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.6.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.4.0</version>
          <configuration>
            <instructions>
              <Bundle-DocURL>${project.url}</Bundle-DocURL>
              <Bundle-Name>${project.name}</Bundle-Name>
              <Bundle-RequiredExecutionEnvironment>${bundle.env}</Bundle-RequiredExecutionEnvironment>
              <Bundle-SymbolicName>${bundle.symbolicName}</Bundle-SymbolicName>
              <Bundle-Vendor>${bundle.vendor}</Bundle-Vendor>
              <Bundle-Version>${bundle.osgiVersion}</Bundle-Version>
              <Export-Package>org.eclipse.aether.internal.*;x-internal:=true,org.eclipse.aether.*</Export-Package>
            </instructions>
          </configuration>
          <executions>
            <execution>
              <id>create-manifest</id>
              <phase>process-test-classes</phase>
              <goals>
                <goal>manifest</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.5.1</version>
          <configuration>
            <source>1.5</source>
            <target>1.5</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <!-- NOTE: Avoiding 2.4+ due to https://github.com/sonatype/m2eclipse-extras/issues/10 -->
          <version>2.3.2</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.8.1</version>
          <configuration>
            <detectOfflineLinks>false</detectOfflineLinks>
            <links>
              <link>http://download.oracle.com/javase/6/docs/api/</link>
            </links>
            <tags>
              <tag>
                <name>noextend</name>
                <placement>a</placement>
                <head>Restriction:</head>
              </tag>
              <tag>
                <name>noimplement</name>
                <placement>a</placement>
                <head>Restriction:</head>
              </tag>
              <tag>
                <name>noinstantiate</name>
                <placement>a</placement>
                <head>Restriction:</head>
              </tag>
              <tag>
                <name>nooverride</name>
                <placement>a</placement>
                <head>Restriction:</head>
              </tag>
              <tag>
                <name>noreference</name>
                <placement>a</placement>
                <head>Restriction:</head>
              </tag>
              <tag>
                <name>provisional</name>
                <placement>a</placement>
                <head>Provisional:</head>
              </tag>
            </tags>
            <groups>
              <group>
                <title>API</title>
                <packages>org.eclipse.aether*</packages>
              </group>
              <group>
                <title>SPI</title>
                <packages>org.eclipse.aether.spi*</packages>
              </group>
              <group>
                <title>Utilities</title>
                <packages>org.eclipse.aether.util*</packages>
              </group>
              <group>
                <title>Repository Connectors</title>
                <packages>org.eclipse.aether.connector*</packages>
              </group>
              <group>
                <title>Transporters</title>
                <packages>org.eclipse.aether.transport*</packages>
              </group>
              <group>
                <title>Implementation</title>
                <packages>org.eclipse.aether.impl*</packages>
              </group>
              <group>
                <title>Internals</title>
                <packages>org.eclipse.aether.internal*</packages>
              </group>
            </groups>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <mavenExecutorId>forked-path</mavenExecutorId>
            <useReleaseProfile>false</useReleaseProfile>
            <goals>deploy javadoc:aggregate-jar assembly:attached</goals>
            <arguments>-Psonatype-oss-release</arguments>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
          <configuration>
            <archive>
              <manifestEntries>
                <Bundle-ManifestVersion>2</Bundle-ManifestVersion>
                <Bundle-Name>${project.name} Sources</Bundle-Name>
                <Bundle-License>http://www.eclipse.org/legal/epl-v10.html</Bundle-License>
                <Bundle-RequiredExecutionEnvironment>${bundle.env}</Bundle-RequiredExecutionEnvironment>
                <Bundle-SymbolicName>${bundle.symbolicName}.source</Bundle-SymbolicName>
                <Bundle-Vendor>${bundle.vendor}</Bundle-Vendor>
                <Bundle-Version>${bundle.osgiVersion}</Bundle-Version>
                <Eclipse-SourceBundle>${bundle.symbolicName};version="${bundle.osgiVersion}";roots:="."</Eclipse-SourceBundle>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.9</version>
          <configuration>
            <argLine>-Xmx128m</argLine>
            <redirectTestOutputToFile>${surefire.redirectTestOutputToFile}</redirectTestOutputToFile>
            <systemPropertyVariables>
              <java.io.tmpdir>${project.build.directory}/surefire-tmp</java.io.tmpdir>
            </systemPropertyVariables>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.9</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java15</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java-1.5-compat</id>
              <phase>process-classes</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>1.5.5</version>
          <executions>
            <execution>
              <id>generate-components-xml</id>
              <phase>process-classes</phase>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.eclipse.sisu</groupId>
          <artifactId>sisu-maven-plugin</artifactId>
          <version>0.0.0.M2</version>
          <executions>
            <execution>
              <id>generate-index</id>
              <phase>process-classes</phase>
              <goals>
                <goal>main-index</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>qa</id>
            <phase>verify</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireProperty>
                  <!-- enforce OSGi-friendly versioning scheme such that milestone < rc < final release -->
                  <property>project.version</property>
                  <regex>[0-9]+\.[0-9]+\.[0-9]+((.*-SNAPSHOT)|(\.M[0-9]+)|(\.RC[0-9]+)|(\.v[0-9]{8}))</regex>
                  <regexMessage>Project version must be either X.Y.Z.M#, X.Y.Z.RC# or X.Y.Z.v########</regexMessage>
                </requireProperty>
                <bannedDependencies>
                  <!-- enforce use of dependencies approved via IP log -->
                  <searchTransitive>true</searchTransitive>
                  <excludes>
                    <exclude>*:*</exclude>
                  </excludes>
                  <includes>
                    <include>org.eclipse.aether</include>
                    <include>org.eclipse.sisu</include>
                    <include>org.eclipse.jetty:*:*:*:test</include>
                    <!-- CQ #5627 -->
                    <include>org.slf4j:slf4j-api:[1.6.2]</include>
                    <!-- CQ #5629 -->
                    <include>org.codehaus.plexus:plexus-component-annotations:[1.5.5]</include>
                    <!-- CQ #5630 -->
                    <include>org.codehaus.plexus:plexus-utils:[2.1]</include>
                    <!-- CQ #5631 -->
                    <include>org.codehaus.plexus:plexus-classworlds:[2.4]</include>
                    <!-- CQ #5632 -->
                    <include>org.apache.maven.wagon:wagon-provider-api:[1.0]</include>
                    <!-- CQ #5636 -->
                    <include>org.sonatype.sisu:sisu-guice:[3.1.6]</include>
                    <!-- CQ #7328 -->
                    <include>com.google.guava:guava:[11.0.2]</include>
                    <!-- CQ #5639 -->
                    <include>javax.inject:javax.inject:[1]</include>
                    <!-- CQ #7657 -->
                    <include>org.apache.httpcomponents:httpclient:[4.2.6]</include>
                    <!-- CQ #7656 -->
                    <include>org.apache.httpcomponents:httpcore:[4.2.5]</include>
                    <!-- CQ #7346 -->
                    <include>commons-codec:commons-codec:[1.6]</include>
                    <!-- CQ #7347 -->
                    <include>org.slf4j:jcl-over-slf4j:[1.6.2]</include>
                    <!-- CQ #7538 -->
                    <include>junit:junit:[4.11]:*:test</include>
                    <include>org.hamcrest:hamcrest-core:[1.3]:*:test</include>
                    <include>org.hamcrest:hamcrest-library:[1.3]:*:test</include>
                    <include>com.googlecode.jmockit:jmockit:[1.3]:*:test</include>
                    <include>ch.qos.logback:logback-core:[1.0.7]:*:test</include>
                    <include>ch.qos.logback:logback-classic:[1.0.7]:*:test</include>
                    <include>org.eclipse.jetty.orbit:javax.servlet:[2.5.0.v201103041518]:*:test</include>
                  </includes>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>osgi-timestamp</id>
            <phase>initialize</phase>
            <goals>
              <goal>timestamp-property</goal>
            </goals>
            <configuration>
              <name>bundle.osgiTimestamp</name>
              <pattern>yyyyMMdd-HHmm</pattern>
              <timeZone>UTC</timeZone>
              <locale>en</locale>
            </configuration>
          </execution>
          <execution>
            <id>osgi-version</id>
            <phase>initialize</phase>
            <goals>
              <goal>regex-property</goal>
            </goals>
            <configuration>
              <name>bundle.osgiVersion</name>
              <value>${project.version}</value>
              <regex>-SNAPSHOT</regex>
              <replacement>.${bundle.osgiTimestamp}</replacement>
              <failIfNoMatch>false</failIfNoMatch>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>sonatype-oss-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <phase>package</phase>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
              <execution>
                <id>default-cli</id>
                <configuration>
                  <attach>false</attach>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.0.2</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>attach-source-release-distro</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileFormat>gnu</tarLongFileFormat>
                </configuration>
              </execution>
              <execution>
                <id>default-cli</id>
                <configuration>
                  <attach>false</attach>
                  <descriptors>
                    <descriptor>src/main/assembly/bin.xml</descriptor>
                  </descriptors>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>snapshot-sources</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>clirr</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>check-api-compat</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>m2e</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.eclipse.m2e</groupId>
              <artifactId>lifecycle-mapping</artifactId>
              <version>1.0.0</version>
              <configuration>
                <lifecycleMappingMetadata>
                  <pluginExecutions>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.eclipse.sisu</groupId>
                        <artifactId>sisu-maven-plugin</artifactId>
                        <versionRange>[0.0.0.M2,)</versionRange>
                        <goals>
                          <goal>test-index</goal>
                          <goal>main-index</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>build-helper-maven-plugin</artifactId>
                        <versionRange>[1.7,)</versionRange>
                        <goals>
                          <goal>regex-property</goal>
                          <goal>timestamp-property</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                  </pluginExecutions>
                </lifecycleMappingMetadata>
              </configuration>
            </plugin>
            <plugin>
              <groupId>org.apache.felix</groupId>
              <artifactId>maven-bundle-plugin</artifactId>
              <configuration>
                <instructions>
                  <Bundle-Version>$(replace;${project.version};-SNAPSHOT;.qualifier)</Bundle-Version>
                </instructions>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>

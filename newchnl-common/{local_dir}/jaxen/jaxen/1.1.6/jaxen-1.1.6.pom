<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>oss-parent</artifactId>
    <groupId>org.sonatype.oss</groupId>
    <version>7</version>
  </parent>

  <groupId>jaxen</groupId>
  <artifactId>jaxen</artifactId>
  <packaging>bundle</packaging>
  <name>jaxen</name>
  <version>1.1.6</version>
  <description>Jaxen is a universal Java XPath engine.</description>
  <url>http://jaxen.codehaus.org/</url>
  <licenses>
    <license>
      <url>http://jaxen.codehaus.org/license.html</url>
    </license>
  </licenses>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/BrowseProject.jspa?id=10022</url>
  </issueManagement>
  <ciManagement>
    <notifiers>
      <notifier>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
  </properties>

  <mailingLists>
    <mailingList>
      <name>Jaxen Users List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://archive.jaxen.codehaus.org/user/</archive>
    </mailingList>
    <mailingList>
      <name>Jaxen Developers List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://archive.jaxen.codehaus.org/dev/</archive>
    </mailingList>
    <mailingList>
      <name>Jaxen Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://archive.jaxen.codehaus.org/scm/</archive>
    </mailingList>
  </mailingLists>

  <developers>
    <developer>
      <id>bob</id>
      <name>Bob McWhirter</name>
      <email><EMAIL></email>
      <organization>The Werken Company</organization>
    </developer>
    <developer>
      <id>jstrachan</id>
      <name>James Strachan</name>
      <email><EMAIL></email>
      <organization>Spiritsoft</organization>
    </developer>
    <developer>
      <id>dmegginson</id>
      <name>David Megginson</name>
      <email><EMAIL></email>
      <organization>Megginson Technologies</organization>
    </developer>
    <developer>
      <id>eboldwidt</id>
      <name>Erwin Bolwidt</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>mbelonga</id>
      <name>Mark A. Belonga</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>cnentwich</id>
      <name>Christian Nentwich</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>purpletech</id>
      <name>Alexander Day Chaffee</name>
      <email><EMAIL></email>
      <organization>Purple Technologies</organization>
    </developer>
    <developer>
      <id>jdvorak</id>
      <name>Jan Dvorak</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>szegedia</id>
      <name>Attila Szegedi</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>proyal</id>
      <name>Peter Royal</name>
      <email><EMAIL></email>
      <url>http://fotap.org/~osi</url>
    </developer>
    <developer>
      <id>ssanders</id>
      <name>Scott Sanders</name>
      <email><EMAIL></email>
      <url>http://dotnot.org/blog</url>
      <organization>dotnot</organization>
    </developer>
    <developer>
      <id>bewins</id>
      <name>Brian Ewins</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>elharo</id>
      <name>Elliotte Rusty Harold</name>
      <email><EMAIL></email>
      <url>http://www.elharo.com/</url>
      <organization>Cafe au Lait</organization>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Ryan Gustafson</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>David Peterson</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Mark Wilson</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jacob Kjome</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Michael Brennan</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jason Hunter</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Brett Mclaughlin</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Bradley S. Huffman</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>K. Ari Krupnikov</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Paul R. Brown</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Guoliang Cao</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jérôme Nègre</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Eddie McGreal</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Steen Lehmann</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Ben McCann</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Don Corley</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:svn:https://svn.codehaus.org/jaxen/trunk/jaxen/</connection>
    <url>http://fisheye.codehaus.org/browse/jaxen/</url>
  </scm>

  <organization>
    <name>Codehaus</name>
    <url>http://codehaus.org</url>
  </organization>

  <build>
    <sourceDirectory>src/java/main</sourceDirectory>
    <testSourceDirectory>src/java/test</testSourceDirectory>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.5.1</version>
        <configuration>
          <debug>true</debug>
          <optimize>true</optimize>
          <showDeprecation>true</showDeprecation>
          <source>1.3</source>
          <target>1.2</target>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.3.7</version>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Bundle-ManifestVersion>2</Bundle-ManifestVersion>
            <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
            <Bundle-Version>${project.version}</Bundle-Version>
            <Export-Package>org.jaxen.*;version=${project.version}</Export-Package>
            <Include-Resource>
              org/w3c/dom/UserDataHandler.class=target/classes/org/w3c/dom/UserDataHandler.class,
              META-INF/LICENSE.txt=LICENSE.txt
            </Include-Resource>
            <Import-Package>
              org.w3c.dom;resolution:=optional,
              *;resolution:=optional
            </Import-Package>
            <Private-Package>!org.w3c.dom</Private-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <descriptorRefs>
            <descriptorRef>project</descriptorRef>
            <descriptorRef>src</descriptorRef>
            <descriptorRef>bin</descriptorRef>
          </descriptorRefs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-repository-plugin</artifactId>
        <version>2.3.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.8.1</version>
        <configuration>
          <excludePackageNames>org.jaxen.saxpath.base,org.jaxen.saxpath.helpers</excludePackageNames>
          <links>
            <link>http://java.sun.com/j2se/1.4.2/docs/api/</link>
          </links>
          <stylesheetfile>${basedir}/src/site/resources/css/javadoc-style.css</stylesheetfile>
          <docencoding>UTF-8</docencoding>
          <stylesheetfile>./xdocs/stylesheets/javadoc-style.css</stylesheetfile>
          <tags>
            <tag>
              <head>To Do:</head>
              <name>todo</name>
              <placement>Xa</placement>
            </tag>
          </tags>
          <docencoding>UTF-8</docencoding>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.12</version>
        <configuration>
          <includes>
            <include>**/*Test.java</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.5.1</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>2.4.0</version>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>dom4j</groupId>
      <artifactId>dom4j</artifactId>
      <version>1.6.1</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>jdom</groupId>
      <artifactId>jdom</artifactId>
      <version>1.0</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>xml-apis</groupId>
      <artifactId>xml-apis</artifactId>
      <version>1.3.02</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>xerces</groupId>
      <artifactId>xercesImpl</artifactId>
      <version>2.6.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>xom</groupId>
      <artifactId>xom</artifactId>
      <version>1.0</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-changelog-plugin</artifactId>
        <version>2.2</version>
        <configuration>
          <type>date</type>
          <dates>
            <date>2007-05-06</date>
          </dates>
          <outputEncoding>UTF-8</outputEncoding>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.9.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.6.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>2.4</version>
      </plugin> 
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>2.4.3</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.5.1</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>2.4.0</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jdepend-maven-plugin</artifactId>
        <version>2.0-beta-2</version>
      </plugin> 
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <dependencyDetailsEnabled>false</dependencyDetailsEnabled>
          <dependencyLocationsEnabled>false</dependencyLocationsEnabled>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>dependencies</report>
              <report>scm</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <distributionManagement>
    <site>
      <id>default</id>
      <name>Default Site</name>
      <url>scp://jaxen.codehaus.org/home/<USER>/jaxen/public_html</url>
    </site>
  </distributionManagement>
</project>

<?xml version="1.0" encoding="UTF-8"?>
<!--
To build, you need to have Maven 2 installed.

To compile, run:

    mvn compile

To run tests, run:

    mvn test

To run one particular test, e.g. TestSomeTest, run:

    mvn test -Dtest=TestSomeTest

To build the jars, run:

    mvn package

To create and upload a release, run:

    mvn deploy

To build the site and upload it, run:

    mvn site:deploy

To perform a complete release, run:

    mvn clean compile package site assembly:assembly deploy site:deploy

To actually upload the artifact to sourceforge, it must be manually ftp'd:

    lftp ftp://upload.sourceforge.net/incoming/ -e "put `ls target/jline-*.zip`"

To make a bundle and request that ibilio upload it, do:

    mvn source:jar javadoc:jar repository:bundle-create

    scp target/jline-*-bundle.jar shell.sourceforge.net:/home/<USER>/j/jl/jline/htdocs

    Make a request like at http://jira.codehaus.org/browse/MAVENUPLOAD-1003

-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
    http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <groupId>jline</groupId>
  <artifactId>jline</artifactId>
  <packaging>jar</packaging>
  <name>JLine</name>
  <version>0.9.94</version>
  <description>JLine is a java library for reading and editing user input in console applications. It features tab-completion, command history, password masking, customizable keybindings, and pass-through handlers to use to chain to other console applications.</description>
  <url>http://jline.sourceforge.net</url>
  <issueManagement>
    <system>sourceforge</system>
    <url>http://sourceforge.net/tracker/?group_id=64033&amp;atid=506056</url>
  </issueManagement>
  <inceptionYear>2002</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>JLine users</name>
      <subscribe>https://lists.sourceforge.net/lists/listinfo/jline-users</subscribe>
      <post><EMAIL></post>
      <archive>http://sourceforge.net/mailarchive/forum.php?forum=jline-users</archive>
    </mailingList>
  </mailingLists>

  <developers>
    <developer>
      <id>mprudhom</id>
      <name>Marc Prud'hommeaux</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>BSD</name>
      <url>LICENSE.txt</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:cvs:pserver:<EMAIL>:/cvsroot/jline:jline</connection>
    <developerConnection>scm:cvs:ext:${maven.username}@jline.cvs.sourceforge.net:/cvsroot/jline:jline</developerConnection>
    <url>http://jline.cvs.sourceforge.net/jline</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <!--
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jalopy-maven-plugin</artifactId>
        <version>1.0-SNAPSHOT</version>
        <configuration>
          <fileFormat>UNIX</fileFormat>
          <convention>codestyle.xml</convention>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>format</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      -->
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- <testFailureIgnore>true</testFailureIgnore> -->
          <useFile>false</useFile>
          <trimStackTrace>false</trimStackTrace>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.3</source>
          <target>1.3</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <stagingDirectory>../site-staging</stagingDirectory>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/assembly.xml</descriptor>
          </descriptors>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jxr-maven-plugin</artifactId>
        <configuration>
          <aggregate>true</aggregate>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <aggregate>true</aggregate>
          <linksource>true</linksource>
          <links>
            <link>http://java.sun.com/j2se/1.5.0/docs/api</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <reportSets>
          <reportSet>
            <reports>
              <!-- <report>dependencies</report> -->
              <!-- <report>cim</report> -->
              <!-- <report>cobertura</report> -->
              <report>project-team</report>
              <report>mailing-list</report>
              <report>issue-tracking</report>
              <report>license</report>
              <report>scm</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>surefire-report-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>
  <distributionManagement>
    <repository>
      <id>jline</id>
      <url>scp://shell.sourceforge.net/home/<USER>/j/jl/jline/htdocs/m2repo</url>
    </repository>
    <snapshotRepository>
      <id>jline</id>
      <url>scp://shell.sourceforge.net/home/<USER>/j/jl/jline/htdocs/m2snapshot</url>
    </snapshotRepository>
    <site>
      <id>jline</id>
      <name>jline</name>
      <url>scpexe://shell.sourceforge.net/home/<USER>/j/jl/jline/htdocs/</url>
    </site>
  </distributionManagement>
</project>

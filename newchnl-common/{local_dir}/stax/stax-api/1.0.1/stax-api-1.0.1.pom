<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>stax</groupId>
  <artifactId>stax-api</artifactId>
  <name>StAX API</name>
  <version>1.0.1</version>
  <description>StAX API is the standard java XML processing API defined by JSR-173</description>
  <url>http://stax.codehaus.org/</url>
  <issueManagement>
    <url>http://jira.codehaus.org/browse/STAX</url>
  </issueManagement>
  <ciManagement>
    <notifiers>
      <notifier>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2005</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <mailingLists>
    <mailingList>
      <name>StAX Builders List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://groups.yahoo.com/group/stax_builders/</archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <id>aslom</id>
      <name>Aleksander Slominski</name>
      <email></email>
      <organization>Indiana University</organization>
    </developer>
    <developer>
      <id>chris</id>
      <name>Chris Fry</name>
      <email></email>
      <organization></organization>
    </developer>
  </developers>
</project>
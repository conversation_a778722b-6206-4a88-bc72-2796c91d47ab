<%--
  Created by IntelliJ IDEA.
  User: caisg
  Date: 2022/2/22
  Time: 5:04 下午
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<html>
<head>
    <title>积分兑换</title>
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">

    <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
</head>
<body style="padding: 10px;">
<!-- here we will place the layout --->
<div id="layoutObj"></div>
<script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
<script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
<script type="text/javascript" src="${ctx}/js/common/config.js"></script>
<script type="text/javascript" src="${ctx}/js/module/points/pointsItem.js"></script>
<script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_export.js"></script>

<script>
    var status = <%=request.getAttribute("status")%>;
    $("#layoutObj").css("height", document.documentElement.clientHeight);
    BUI.use([ 'module/points/channelPointsExchangeCheck' ], function(ChannelPointsExchangeCheck) {
        new ChannelPointsExchangeCheck();
    });
</script>
</body>
</html>
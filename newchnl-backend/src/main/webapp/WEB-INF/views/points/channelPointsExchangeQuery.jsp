<%--
  Created by IntelliJ IDEA.
  User: dull
  Date: 2022/3/24
  Time: 15:06
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@include file="/common/header.jsp"%>
<%@include file="/common/hearderUtil.jsp"%>
<html>
    <head>
        <title>激励积分兑换工单查询</title>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/dhtmlx.css">
        <link href="${ctx}/assets/bui/css/bs3/dpl-min.css" rel="stylesheet" type="text/css"/>
        <link href="${ctx}/assets/bui/css/bs3/bui-min.css" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn_bricks.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/default/easyui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/assets/easyui/themes/icon.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/common.css">
    </head>
    <body style="padding: 10px;">
        <!-- here we will place the layout --->
        <div id="layoutObj"></div>
        <script type="text/javascript" src="${ctx}/assets/jquery/jquery.min.js"></script>
        <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/dhtmlx.js"></script>
        <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_json.js"></script>
        <script type="text/javascript" src="${ctx}/assets/dhtmlx/codebase/ext/dhtmlxgrid_pgn.js"></script>
        <script type="text/javascript" src="${ctx}/assets/bui/seed-min.js" data-debug="true"></script>
        <script type="text/javascript" src="${ctx}/js/common/config.js"></script>
        <script type="text/javascript" src="${ctx}/js/module/points/pointsItem.js"></script>

        <script>
            $("#layoutObj").css("height", document.documentElement.clientHeight);
            BUI.use([ 'module/points/channelPointsExchangeQuery' ], function(ChannelPointsExchangeQuery) {
                new ChannelPointsExchangeQuery();
            });
        </script>
    </body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<project>

 <!-- General information -->

  <modelVersion>4.0.0</modelVersion>
  <groupId>org.codehaus.jackson</groupId>
  <artifactId>jackson-core-lgpl</artifactId>
  <packaging>jar</packaging>
  <name><PERSON></name>
  <version>1.4.0</version>
  <description><PERSON> is a high-performance JSON processor (parser, generator)</description>

 <!-- Contact information -->

  <url>http://jackson.codehaus.org</url>
  <issueManagement>
    <url>http://jira.codehaus.org/browse/JACKSON</url>
  </issueManagement>

 <!-- Dependency information -->
 
  <dependencies>
    <!-- no dependencies, for now -->
  </dependencies>

  <!-- Licensing -->
  <licenses>
    <license>
      <name>GNU Lesser General Public License (LGPL), Version 2.1</name>
      <url>http://www.fsf.org/licensing/licenses/lgpl.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>FasterXML</name>
    <url>http://fasterxml.com</url>
  </organization>
</project>

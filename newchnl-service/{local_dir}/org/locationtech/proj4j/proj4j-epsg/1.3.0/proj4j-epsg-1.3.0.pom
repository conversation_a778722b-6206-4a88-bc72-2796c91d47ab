<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.locationtech.proj4j</groupId>
    <artifactId>proj4j-epsg</artifactId>
    <version>1.3.0</version>
    <packaging>jar</packaging>
    <name>Proj4J EPSG</name>
    <url>https://github.com/locationtech/proj4j</url>
    <description>Java port of the Proj.4 library for coordinate reprojection</description>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
        <license>
            <name>EPSG database distribution license</name>
            <url>https://raw.githubusercontent.com/locationtech/proj4j/master/LICENSE.EPSG</url>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/locationtech/proj4j.git</url>
        <connection>scm:git:https://github.com/locationtech/proj4j.git</connection>
        <tag>HEAD</tag>
    </scm>

    <developers>
        <developer>
            <id>echeipesh</id>
            <name>Eugene Cheipesh</name>
            <url>https://github.com/echeipesh</url>
        </developer>
        <developer>
            <id>lossyrob</id>
            <name>Rob Emanuele</name>
            <url>https://github.com/lossyrob</url>
        </developer>
        <developer>
            <id>pomadchin</id>
            <name>Grigory Pomadchin</name>
            <url>https://github.com/pomadchin</url>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>Martin Davis</name>
            <url>https://github.com/dr-jts</url>
        </contributor>
    </contributors>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <debug>true</debug>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <inherited>true</inherited>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <failOnError>true</failOnError>
                            <failOnWarnings>false</failOnWarnings>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>

            <!-- Maven Central Publish -->
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <updateReleaseInfo>true</updateReleaseInfo>
                </configuration>
                <executions>
                    <execution>
                        <id>default-deploy</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>deploy</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>eclipse</id>
            <distributionManagement>
                <repository>
                    <id>repo.eclipse.org</id>
                    <name>Proj4J Repository - Releases</name>
                    <url>https://repo.eclipse.org/content/repositories/proj4j-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>repo.eclipse.org</id>
                    <name>Proj4J Repository - Snapshots</name>
                    <url>https://repo.eclipse.org/content/repositories/proj4j-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>central</id>
            <distributionManagement>
                <repository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.6.13</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>false</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.0.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <keyname>BAE04B4DA2C66578</keyname>
                                    <passphrase>ok1n@wa</passphrase>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>

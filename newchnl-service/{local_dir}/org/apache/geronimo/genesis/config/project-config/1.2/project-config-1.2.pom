<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at
    
     http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<!-- $Rev: 561276 $ $Date: 2007-07-31 05:01:46 -0400 (Tue, 31 Jul 2007) $ -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>org.apache.geronimo.genesis.config</groupId>
        <artifactId>config</artifactId>
        <version>1.2</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <artifactId>project-config</artifactId>
    <name>Genesis Configuration :: Project</name>
    <packaging>pom</packaging>
    
    <description>
        Genesis project-config provides the common project configuration shared
        by all Apache Geronimo projects.
    </description>
    
    <url>http://geronimo.apache.org</url>
    
    <organization>
        <name>Apache Software Foundation</name>
        <url>http://www.apache.org</url>
    </organization>
    
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    
    <!--
    NOTE: Project pom should define something like:
    
    <scm>
        <connection>scm:svn:http://svn.apache.org/repos/asf/geronimo</connection>
        <developerConnection>scm:svn:https://svn.apache.org/repos/asf/geronimo</developerConnection>
        <url>https://svn.apache.org/viewvc/geronimo</url>
    </scm>
    -->
    
    <mailingLists>
        <mailingList>
            <name>Geronimo User List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/geronimo-user</archive>
            <otherArchives>
                <otherArchive>http://www.nabble.com/Apache-Geronimo---Users-f135.html</otherArchive>
            </otherArchives>
        </mailingList>
        
        <mailingList>
            <name>Geronimo Developer List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/geronimo-dev</archive>
            <otherArchives>
                <otherArchive>http://www.nabble.com/Apache-Geronimo---Dev-f136.html</otherArchive>
            </otherArchives>
        </mailingList>
        
        <mailingList>
            <name>Source Control List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/geronimo-scm</archive>
        </mailingList>
        
        <!-- ASF-wide lists -->
        
        <mailingList>
            <name>Apache Announce List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/www-announce/</archive>
        </mailingList>
    </mailingLists>
    
    <issueManagement>
        <system>jira</system>
        <url>http://issues.apache.org/jira/browse/GERONIMO</url>
    </issueManagement>
    
    <ciManagement>
        <system>continuum</system>
        <notifiers>
            <notifier>
                <type>mail</type>
                <sendOnError />
                <sendOnFailure />
                <address><EMAIL></address>
            </notifier>
        </notifiers>
    </ciManagement>
    
    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>3.8.1</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.geronimo.genesis.config</groupId>
                <artifactId>logging-config</artifactId>
                <version>1.2</version>
            </dependency>
            
        </dependencies>
    </dependencyManagement>
    
    <dependencies>
        <dependency>
            <groupId>org.apache.geronimo.genesis.config</groupId>
            <artifactId>logging-config</artifactId>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <defaultGoal>install</defaultGoal>
        
        <sourceDirectory>${pom.basedir}/src/main/java</sourceDirectory>
        <testSourceDirectory>${pom.basedir}/src/test/java</testSourceDirectory>
        
        <resources>
            <resource>
                <directory>${pom.basedir}/src/main/resources</directory>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>${pom.basedir}/src/test/resources</directory>
            </testResource>
            
            <!--
            TODO: Drop this... its not got much meaning
            -->
            <testResource>
                <directory>${pom.basedir}/src/test/data</directory>
            </testResource>
        </testResources>
        
        <extensions>
            <extension>
                <groupId>org.apache.geronimo.genesis.config</groupId>
                <artifactId>checkstyle-config</artifactId>
                <version>1.2</version>
            </extension>
            
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-ssh</artifactId>
                <version>1.0-beta-2</version>
            </extension>
        </extensions>
            
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.2</version>
                    <configuration>
                        <forkMode>once</forkMode>
                        <argLine>-enableassertions -Dgeronimo.bootstrap.logging.enabled=false -Dlog4j.configuration=org/apache/geronimo/test-log4j.properties</argLine>
                        <workingDirectory>${project.build.directory}</workingDirectory>
                        
                        <!-- Override the default, do not accept Test* as test classes -->
                        <excludes>
                            <exclude>**/Abstract*.java</exclude>
                            <exclude>**/Test*.java</exclude>
                        </excludes>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.1</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-one-plugin</artifactId>
                    <version>1.0</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.1</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.1</version>
                    <configuration>
                        <tarLongFileMode>gnu</tarLongFileMode>
                    </configuration>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.1</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clover-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.0-beta-4</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-one-plugin</artifactId>
                    <version>1.0</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.1</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-rar-plugin</artifactId>
                    <version>2.1</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.0.1</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven</groupId>
                            <artifactId>maven-archiver</artifactId>
                            <!--
                            FIXME: 2.1 is broken... won't allow custom manifestEntires, need to use
                                   until war plugin is updated.
                            -->
                            <version>2.2</version>
                        </dependency>
                    </dependencies>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-idea-plugin</artifactId>
                    <version>2.0</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>jspc-maven-plugin</artifactId>
                    <version>1.4.5</version>
                </plugin>
                
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>groovy-maven-plugin</artifactId>
                    <version>1.0-alpha-2</version>
                </plugin>
                
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.0</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.0-alpha-1</version>
                </plugin>
                
                <!--
                NOTE: Using constant versions here instead of ${pom.version}
                      so that the release plugin can update the version for us.
                -->
                
                <plugin>
                    <groupId>org.apache.geronimo.genesis.plugins</groupId>
                    <artifactId>tools-maven-plugin</artifactId>
                    <version>1.2</version>
                </plugin>
                
                <plugin>
                    <groupId>org.apache.geronimo.genesis.plugins</groupId>
                    <artifactId>maven-maven-plugin</artifactId>
                    <version>1.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
        
        <plugins>
            <!--
            NOTE: Project pom should define:
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.4</source>
                    <target>1.4</target>
                </configuration>
            </plugin>
             
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-idea-plugin</artifactId>
                <configuration>
                    <jdkName>1.4</jdkName>
                    <linkModules>true</linkModules>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <tagBase>https://svn.apache.org/repos/asf/geronimo/tags</tagBase>
                </configuration>
            </plugin>
            -->
         </plugins>
    </build>
    
    <repositories>
        <repository>
            <id>apache-snapshots</id>
            <name>Apache Snapshots Repository</name>
            <url>http://people.apache.org/repo/m2-snapshot-repository</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        
        <repository>
            <id>apache-incubator</id>
            <name>Apache Incubator Repository</name>
            <url>http://people.apache.org/repo/m2-incubating-repository/</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots Repository</name>
            <url>http://snapshots.repository.codehaus.org</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>
    
    <pluginRepositories>
        <pluginRepository>
            <id>apache-snapshots</id>
            <name>Apache Snapshots Repository</name>
            <url>http://people.apache.org/repo/m2-snapshot-repository</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </pluginRepository>
        
        <pluginRepository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots Repository</name>
            <url>http://snapshots.repository.codehaus.org</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>
    
    <profiles>
        <profile>
            <id>default</id>
            
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        
        <profile>
            <id>tools</id>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.geronimo.genesis.plugins</groupId>
                        <artifactId>tools-maven-plugin</artifactId>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.geronimo.genesis.plugins</groupId>
                        <artifactId>maven-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>2.0</version>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <minmemory>128m</minmemory>
                    <maxmemory>512</maxmemory>
                    <breakiterator>true</breakiterator>
                    <quiet>true</quiet>
                    <verbose>false</verbose>
                    <source>1.4</source>
                    <!--
                    FIXME: This worked in m1, but m2 complains about it being an invalid flag
                    
                    <additionalparam>-J-Djava.awt.headless=true</additionalparam>
                    -->
                    <linksource>true</linksource>
                    <links>
                        <!-- J2SE -->
                        <link>http://java.sun.com/j2se/1.5.0/docs/api/</link>
                        <link>http://java.sun.com/j2se/1.4.2/docs/api/</link>
                        <link>http://java.sun.com/j2se/1.3/docs/api/</link>
                        
                        <!-- JavaEE -->
                        <link>http://java.sun.com/javaee/5/docs/api/</link>
                        <link>http://java.sun.com/j2ee/1.4/docs/api/</link>
                        <link>http://java.sun.com/j2ee/sdk_1.3/techdocs/api/</link>
                        
                        <!-- Libraries -->
                        <link>http://jakarta.apache.org/commons/collections/apidocs</link>
                        <link>http://jakarta.apache.org/commons/logging/apidocs/</link>
                        <link>http://www.junit.org/junit/javadoc/</link>
                        <link>http://logging.apache.org/log4j/docs/api/</link>
                        <link>http://jakarta.apache.org/regexp/apidocs/</link>
                        <link>http://jakarta.apache.org/velocity/api/</link>
                    </links>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>2.0</version>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>2.1</version>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.0.1</version>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <!-- Pulled as resource from checkstyle-config plugin -->
                    <configLocation>org/apache/geronimo/checkstyle.xml</configLocation>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

</project>

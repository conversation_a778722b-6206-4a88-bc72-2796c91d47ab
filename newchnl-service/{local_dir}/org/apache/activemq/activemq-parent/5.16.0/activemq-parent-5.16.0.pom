<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>18</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.apache.activemq</groupId>
  <artifactId>activemq-parent</artifactId>
  <version>5.16.0</version>
  <packaging>pom</packaging>
  <name>ActiveMQ</name>
  <inceptionYear>2005</inceptionYear>

  <properties>
    <activeio-version>3.1.4</activeio-version>
    <siteId>activemq-${project.version}</siteId>
    <projectName>Apache ActiveMQ</projectName>
    <!-- base url for site deployment.  See distribution management for full url.  Override this in settings.xml for staging -->
    <staging.siteURL>scp://people.apache.org/x1/www/activemq.apache.org</staging.siteURL>

    <!-- JAR dependency versions -->
    <activemq-protobuf-version>1.1</activemq-protobuf-version>
    <activesoap-version>1.3</activesoap-version>
    <annogen-version>0.1.0</annogen-version>
    <ant-version>1.10.7</ant-version>
    <ant-bundle-version>1.10.7_1</ant-bundle-version>
    <aopalliance-version>1.0</aopalliance-version>
    <aries-version>1.1.0</aries-version>
    <aries-transaction-version>1.1.1</aries-transaction-version>
    <axion-version>1.0-M3-dev</axion-version>
    <camel-version>2.24.3</camel-version>
    <camel-version-range>[2.20,3)</camel-version-range>
    <cglib-version>2.2</cglib-version>
    <commons-beanutils-version>1.9.4</commons-beanutils-version>
    <commons-collections-version>3.2.2</commons-collections-version>
    <commons-daemon-version>1.2.2</commons-daemon-version>
    <commons-dbcp2-version>2.7.0</commons-dbcp2-version>
    <commons-io-version>2.6</commons-io-version>
    <commons-lang-version>2.6</commons-lang-version>
    <commons-logging-version>1.2</commons-logging-version>
    <commons-pool2-version>2.8.0</commons-pool2-version>
    <commons-primitives-version>1.0</commons-primitives-version>
    <commons-net-version>3.6</commons-net-version>
    <directory-version>2.0.0.AM25</directory-version>
    <ecj.version>3.17.0</ecj.version>
    <ftpserver-version>1.1.1</ftpserver-version>
    <geronimo-version>1.0</geronimo-version>
    <guava-version>28.2-jre</guava-version>
    <hadoop-version>1.2.1</hadoop-version>
    <hawtbuf-version>1.11</hawtbuf-version>
    <hawtdispatch-version>1.22</hawtdispatch-version>
    <howl-version>0.1.8</howl-version>
    <hsqldb-version>1.8.0.12</hsqldb-version>
    <httpclient-version>4.5.11</httpclient-version>
    <httpcore-version>4.4.13</httpcore-version>
    <insight-version>1.2.0.Beta4</insight-version>
    <jackson-version>2.9.10</jackson-version>
    <jackson-databind-version>2.9.10.4</jackson-databind-version>
    <jasypt-version>1.9.3</jasypt-version>
    <jaxb-bundle-version>2.2.11_1</jaxb-bundle-version>
    <jetty9-version>9.4.28.v20200408</jetty9-version>
    <jetty-version>${jetty9-version}</jetty-version>
    <jmdns-version>3.4.1</jmdns-version>
    <tomcat-api-version>9.0.35</tomcat-api-version>
    <jettison-version>1.4.1</jettison-version>
    <jmock-version>2.5.1</jmock-version>
    <jolokia-version>1.6.2</jolokia-version>
    <josql-version>1.5_5</josql-version>
    <!-- for json-simple use same version as jolokia uses -->
    <json-simple-version>1.1.1</json-simple-version>
    <junit-version>4.12</junit-version>
    <hamcrest-version>1.3</hamcrest-version>
    <jxta-version>2.0</jxta-version>
    <karaf-version>4.2.8</karaf-version>
    <leveldb-api-version>0.9</leveldb-api-version>
    <leveldb-version>0.9</leveldb-version>
    <leveldbjni-version>1.8</leveldbjni-version>
    <log4j-version>1.2.17</log4j-version>
    <mockito-version>1.10.19</mockito-version>
    <owasp-dependency-check-version>5.2.4</owasp-dependency-check-version>
    <powermock-version>1.6.5</powermock-version>
    <mqtt-client-version>1.16</mqtt-client-version>
    <openjpa-version>1.2.0</openjpa-version>
    <org-apache-derby-version>10.14.2.0</org-apache-derby-version>
    <osgi-version>6.0.0</osgi-version>
    <p2psockets-version>1.1.2</p2psockets-version>
    <linkedin-zookeeper-version>1.4.0</linkedin-zookeeper-version>
    <zookeeper-version>3.4.14</zookeeper-version>
    <qpid-proton-version>0.33.5</qpid-proton-version>
    <qpid-jms-version>0.52.0</qpid-jms-version>
    <qpid-jms-netty-version>4.1.46.Final</qpid-jms-netty-version>
    <qpid-jms-proton-version>0.33.5</qpid-jms-proton-version>
    <netty-all-version>4.1.46.Final</netty-all-version>
    <regexp-version>1.3</regexp-version>
    <rome-version>1.12.2</rome-version>
    <saxon-version>9.5.1-5</saxon-version>
    <saxon-bundle-version>9.5.1-5_1</saxon-bundle-version>
    <scala-plugin-version>3.1.0</scala-plugin-version>
    <scala-version>2.11.11</scala-version>
    <shiro-version>1.5.3</shiro-version>
    <scalatest-version>3.0.8</scalatest-version>
    <slf4j-version>1.7.30</slf4j-version>
    <snappy-version>1.1.2</snappy-version>
    <spring-version>4.3.26.RELEASE</spring-version>
    <taglibs-version>1.2.5</taglibs-version>
    <velocity-version>2.2</velocity-version>
    <xalan-version>2.7.2</xalan-version>
    <xmlbeans-version>3.1.0</xmlbeans-version>
    <xmlbeans-bundle-version>2.6.0_2</xmlbeans-bundle-version>
    <xpp3-version>1.1.4c</xpp3-version>
    <xstream-version>1.4.11.1</xstream-version>
    <xbean-version>4.17</xbean-version>
    <xerces-version>2.12.0</xerces-version>
    <jaxb-basics-version>0.6.4</jaxb-basics-version>
    <stompjms-version>1.19</stompjms-version>

    <pax-exam-version>4.13.1</pax-exam-version>
    <pax-url-version>2.6.2</pax-url-version>
    <felix-framework-version>5.6.12</felix-framework-version>

    <site-repo-url>scpexe://people.apache.org/www/activemq.apache.org/maven/</site-repo-url>
    <source-version>1.8</source-version>
    <target-version>1.8</target-version>
    <javase-javadoc-url>http://docs.oracle.com/javase/8/docs/api/</javase-javadoc-url>
    <javaee-javadoc-url>http://download.oracle.com/javaee/6/api/</javaee-javadoc-url>
    <slf4j-javadoc-url>http://www.slf4j.org/apidocs/</slf4j-javadoc-url>

    <!-- Maven Plugin Version for this Project -->
    <maven-bundle-plugin-version>2.3.7</maven-bundle-plugin-version>
    <maven-surefire-plugin-version>2.22.1</maven-surefire-plugin-version>
    <maven-antrun-plugin-version>1.3</maven-antrun-plugin-version>
    <maven-assembly-plugin-version>2.4</maven-assembly-plugin-version>
    <maven-release-plugin-version>2.4.1</maven-release-plugin-version>
    <maven-eclipse-plugin-version>2.10</maven-eclipse-plugin-version>
    <maven-war-plugin-version>2.4</maven-war-plugin-version>
    <maven-compiler-plugin-version>3.8.1</maven-compiler-plugin-version>
    <maven-jar-plugin-version>2.4</maven-jar-plugin-version>
    <maven-archiver-version>2.5</maven-archiver-version>
    <maven-source-plugin-version>2.2.1</maven-source-plugin-version>
    <maven-javadoc-plugin-version>2.9.1</maven-javadoc-plugin-version>
    <maven-install-plugin-version>2.4</maven-install-plugin-version>
    <maven-shade-plugin-version>2.1</maven-shade-plugin-version>
    <findbugs-maven-plugin-version>3.0.1</findbugs-maven-plugin-version>
    <javacc-maven-plugin-version>2.6</javacc-maven-plugin-version>
    <cobertura-maven-plugin-version>2.5.2</cobertura-maven-plugin-version>
    <taglist-maven-plugin-version>2.4</taglist-maven-plugin-version>
    <build-helper-maven-plugin-version>1.8</build-helper-maven-plugin-version>
    <apache-rat-plugin-version>0.11</apache-rat-plugin-version>
    <ianal-maven-plugin-version>1.0-alpha-1</ianal-maven-plugin-version>
    <depends-maven-plugin-version>1.2</depends-maven-plugin-version>
    <maven-dependency-plugin-version>2.8</maven-dependency-plugin-version>
    <maven-project-info-reports-plugin-version>2.7</maven-project-info-reports-plugin-version>
    <maven-graph-plugin-version>1.30</maven-graph-plugin-version>
    <maven-plugin-plugin-version>3.6.0</maven-plugin-plugin-version>
    <maven-core-version>3.6.1</maven-core-version>
    <!-- OSGi bundles properties -->
    <activemq.osgi.import.pkg>*</activemq.osgi.import.pkg>
    <activemq.osgi.export.pkg>org.apache.activemq*</activemq.osgi.export.pkg>
    <activemq.osgi.private.pkg>!*</activemq.osgi.private.pkg>
    <activemq.osgi.export>${activemq.osgi.export.pkg}*;version=${activemq.osgi.export.version};-noimport:=true</activemq.osgi.export>
    <activemq.osgi.export.version>${project.version}</activemq.osgi.export.version>
    <activemq.osgi.import>${activemq.osgi.import.pkg}</activemq.osgi.import>
    <activemq.osgi.dynamic.import />
    <activemq.osgi.symbolic.name>${project.groupId}.${project.artifactId}</activemq.osgi.symbolic.name>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <url>http://activemq.apache.org</url>

  <issueManagement>
    <system>Jira</system>
    <url>https://issues.apache.org/jira/browse/AMQ</url>
  </issueManagement>

  <mailingLists>
    <mailingList>
      <name>User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
    </mailingList>
    <mailingList>
      <name>Development List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
    </mailingList>
  </mailingLists>

  <distributionManagement>
    <site>
      <!-- this needs to match a server in your settings.xml with upload settings -->
      <id>activemq-website</id>
      <!-- set the staging.siteURL in your ~/.m2/settings.xml in a release or other profile -->
      <url>${staging.siteURL}/maven/${project.version}</url>
      <!--<url>${site-repo-url}</url>-->
    </site>
    <snapshotRepository>
      <id>apache.snapshots.https</id>
      <name>Apache Development Snapshot Repository</name>
      <url>https://repository.apache.org/content/repositories/snapshots</url>
      <uniqueVersion>false</uniqueVersion>
    </snapshotRepository>
  </distributionManagement>

  <modules>
    <module>activemq-openwire-generator</module>
    <module>activemq-client</module>
    <module>activemq-openwire-legacy</module>
    <module>activemq-broker</module>
    <module>activemq-stomp</module>
    <module>activemq-mqtt</module>
    <module>activemq-amqp</module>
    <module>activemq-kahadb-store</module>
    <module>activemq-jdbc-store</module>
    <module>activemq-leveldb-store</module>
    <module>activemq-unit-tests</module>
    <module>activemq-all</module>
    <module>activemq-camel</module>
    <module>activemq-console</module>
    <module>activemq-jaas</module>
    <module>activemq-karaf</module>
    <module>activemq-jms-pool</module>
    <module>activemq-pool</module>
    <module>activemq-cf</module>
    <module>activemq-ra</module>
    <module>activemq-rar</module>
    <module>activemq-run</module>
    <module>activemq-shiro</module>
    <module>activemq-spring</module>
    <module>activemq-runtime-config</module>
    <module>activemq-tooling</module>
    <module>activemq-web</module>
    <module>activemq-partition</module>
    <module>activemq-osgi</module>
    <module>activemq-blueprint</module>
    <module>activemq-web-demo</module>
    <module>activemq-web-console</module>
    <module>activemq-karaf-itest</module>
    <module>activemq-itests-spring31</module>
    <module>assembly</module>
    <module>activemq-log4j-appender</module>
    <module>activemq-http</module>
  </modules>

  <scm>
    <connection>scm:git:http://gitbox.apache.org/repos/asf/activemq.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/activemq.git</developerConnection>
    <url>https://github.com/apache/activemq</url>
    <tag>activemq-5.16.0</tag>
  </scm>

  <dependencyManagement>
    <dependencies>
      <!-- =============================== -->
      <!-- Internal ActiveMQ Dependencies -->
      <!-- =============================== -->
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-amqp</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-http</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-log4j-appender</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-amq-store</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-kahadb-store</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jdbc-store</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-leveldb-store</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-mqtt</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-stomp</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-legacy</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-all</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-camel</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-partition</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq.tooling</groupId>
        <artifactId>activemq-junit</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-unit-tests</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jaas</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jms-pool</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-pool</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-shiro</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-spring</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-runtime-config</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web-demo</artifactId>
        <version>${project.version}</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web-console</artifactId>
        <version>${project.version}</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-console</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-ra</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-rar</artifactId>
        <version>${project.version}</version>
        <type>rar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-run</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web</artifactId>
        <version>${project.version}</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activeio-core</artifactId>
        <version>${activeio-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activeio-core</artifactId>
        <version>${activeio-version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-generator</artifactId>
        <version>${project.version}</version>
      </dependency>
     <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-karaf</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq.protobuf</groupId>
        <artifactId>activemq-protobuf</artifactId>
        <version>${activemq-protobuf-version}</version>
      </dependency>

      <!-- =============================== -->
      <!-- Required dependencies -->
      <!-- =============================== -->
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>${commons-logging-version}</version>
        <exclusions>
          <exclusion>
            <groupId>avalon-framework</groupId>
            <artifactId>avalon-framework</artifactId>
          </exclusion>
          <exclusion>
            <groupId>logkit</groupId>
            <artifactId>logkit</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-jms_1.1_spec</artifactId>
        <version>1.1.1</version>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-jta_1.1_spec</artifactId>
        <version>1.1.1</version>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-j2ee-management_1.1_spec</artifactId>
        <version>1.0.1</version>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-jacc_1.1_spec</artifactId>
        <version>1.0.2</version>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-j2ee-connector_1.5_spec</artifactId>
        <version>2.0.0</version>
      </dependency>
     <dependency>
       <groupId>org.ow2.asm</groupId>
       <artifactId>asm</artifactId>
       <version>7.3.1</version>
     </dependency>

      <!-- Servlet 3.1 and JSP -->
      <dependency>
          <groupId>org.apache.tomcat</groupId>
          <artifactId>tomcat-jsp-api</artifactId>
          <version>${tomcat-api-version}</version>
      </dependency>
       <dependency>
          <groupId>org.apache.tomcat</groupId>
          <artifactId>tomcat-servlet-api</artifactId>
          <version>${tomcat-api-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-websocket-api</artifactId>
        <version>${tomcat-api-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-annotation_1.0_spec</artifactId>
        <version>1.1.1</version>
      </dependency>


      <!-- =============================== -->
      <!-- Optional dependencies -->
      <!-- =============================== -->

      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-core</artifactId>
        <version>${camel-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-spring</artifactId>
        <version>${camel-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-blueprint</artifactId>
        <version>${camel-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-jms</artifactId>
        <version>${camel-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-test</artifactId>
        <version>${camel-version}</version>
      </dependency>
       <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-test-spring</artifactId>
        <version>${camel-version}</version>
      </dependency>

      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>osgi.core</artifactId>
        <version>${osgi-version}</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>osgi.cmpn</artifactId>
        <version>${osgi-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper-version}</version>
      </dependency>

      <!-- zeroconf transport -->
      <dependency>
        <groupId>javax.jmdns</groupId>
        <artifactId>jmdns</artifactId>
        <version>${jmdns-version}</version>
      </dependency>

      <!-- For jsvc support -->
      <dependency>
        <groupId>commons-daemon</groupId>
        <artifactId>commons-daemon</artifactId>
        <version>${commons-daemon-version}</version>
      </dependency>

      <!-- camel testing -->
      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-core</artifactId>
        <version>${camel-version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.camel</groupId>
        <artifactId>camel-spring</artifactId>
        <version>${camel-version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <!--  for custom XML parsing -->
      <dependency>
        <groupId>org.apache.xbean</groupId>
        <artifactId>xbean-spring</artifactId>
        <version>${xbean-version}</version>
        <exclusions>
          <exclusion>
            <groupId>qdox</groupId>
            <artifactId>qdox</artifactId>
          </exclusion>
        </exclusions>

      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson-version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson-version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson-databind-version}</version>
      </dependency>

      <!-- Used to configure the activemq logs -->
      <dependency>
        <groupId>log4j</groupId>
        <artifactId>log4j</artifactId>
        <version>${log4j-version}</version>
        <scope>runtime</scope>
      </dependency>

      <!-- used to support optional transport configuration via URI query strings -->

      <dependency>
        <groupId>commons-beanutils</groupId>
        <artifactId>commons-beanutils</artifactId>
        <version>${commons-beanutils-version}</version>
      </dependency>

      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>${commons-collections-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.openjpa</groupId>
        <artifactId>openjpa-persistence-jdbc</artifactId>
        <version>${openjpa-version}</version>
      </dependency>

      <!-- Optional Shiro Support -->
      <dependency>
        <groupId>org.apache.shiro</groupId>
        <artifactId>shiro-core</artifactId>
        <version>${shiro-version}</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>org.apache.shiro</groupId>
        <artifactId>shiro-spring</artifactId>
        <version>${shiro-version}</version>
        <optional>true</optional>
      </dependency>

      <!-- Optional Spring Support -->
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aop</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-beans</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-expression</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jms</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-tx</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <!--
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring</artifactId>
        <version>${spring-version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.resource</groupId>
            <artifactId>connector</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.transaction</groupId>
            <artifactId>jta</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-support</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-hibernate</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-remoting</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      -->
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-test</artifactId>
        <version>${spring-version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-oxm</artifactId>
        <version>${spring-version}</version>
      </dependency>

      <!-- Optional Derby support-->
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derby</artifactId>
        <version>${org-apache-derby-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbynet</artifactId>
        <version>${org-apache-derby-version}</version>
      </dependency>

      <!-- Optional Axion support -->
      <dependency>
        <groupId>axion</groupId>
        <artifactId>axion</artifactId>
        <version>${axion-version}</version>
      </dependency>

      <dependency>
        <groupId>commons-primitives</groupId>
        <artifactId>commons-primitives</artifactId>
        <version>${commons-primitives-version}</version>
      </dependency>

      <dependency>
        <groupId>regexp</groupId>
        <artifactId>regexp</artifactId>
        <version>${regexp-version}</version>
      </dependency>

      <!-- Optional HSQL DB Support -->
      <!--
        <dependency>
        <groupId>hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>${hsqldb-version}</version>
        </dependency>
      -->

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>${commons-dbcp2-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>${commons-pool2-version}</version>
      </dependency>

      <!-- Optional Journal Implementation -->
      <!--
        <dependency>
        <groupId>howl</groupId>
        <artifactId>howl-logger</artifactId>
        <version>${howl-version}</version>
        </dependency>
      -->

      <!-- Optional Jabber support -->
      <dependency>
        <groupId>activemq</groupId>
        <artifactId>smack</artifactId>
        <version>1.5.0</version>
      </dependency>

      <dependency>
        <groupId>activemq</groupId>
        <artifactId>smackx</artifactId>
        <version>1.5.0</version>
      </dependency>

      <!-- =============================== -->
      <!-- XML processing dependencies -->
      <!-- =============================== -->
      <!-- For XMLBeans -->
      <dependency>
        <groupId>org.apache.xmlbeans</groupId>
        <artifactId>xmlbeans</artifactId>
        <version>${xmlbeans-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.xmlbeans</groupId>
        <artifactId>xmlbeans-xpath</artifactId>
        <version>${xmlbeans-version}</version>
      </dependency>

      <!-- To use XPath using JAXP 1.3 (std in Java 5) -->
      <dependency>
        <groupId>activesoap</groupId>
        <artifactId>jaxp-api</artifactId>
        <version>${activesoap-version}</version>
      </dependency>

      <dependency>
        <groupId>xalan</groupId>
        <artifactId>xalan</artifactId>
        <version>${xalan-version}</version>
      </dependency>

      <dependency>
        <groupId>com.thoughtworks.xstream</groupId>
        <artifactId>xstream</artifactId>
        <version>${xstream-version}</version>
        <exclusions>
          <exclusion>
            <!-- xom is an optional dependency of xstream. Its also
              LGPL, so its really not ASF compatible. -->
            <groupId>xom</groupId>
            <artifactId>xom</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>xpp3</groupId>
        <artifactId>xpp3</artifactId>
        <version>${xpp3-version}</version>
      </dependency>


      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jsp</artifactId>
        <version>${jetty-version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jstl</artifactId>
        <version>${jetty-version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty.aggregate</groupId>
        <artifactId>jetty-all</artifactId>
        <version>${jetty-version}</version>
        <classifier>uber</classifier>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-server</artifactId>
        <version>${jetty-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${httpcore-version}</version>
      </dependency>


      <!-- Tag Libs -->
      <dependency>
        <groupId>org.apache.taglibs</groupId>
        <artifactId>taglibs-standard-spec</artifactId>
        <version>${taglibs-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.taglibs</groupId>
        <artifactId>taglibs-standard-impl</artifactId>
        <version>${taglibs-version}</version>
      </dependency>

      <dependency>
        <groupId>aopalliance</groupId>
        <artifactId>aopalliance</artifactId>
        <version>${aopalliance-version}</version>
      </dependency>

      <dependency>
        <groupId>org.jasypt</groupId>
        <artifactId>jasypt</artifactId>
        <version>${jasypt-version}</version>
      </dependency>

      <!-- testing dependencies -->
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-core</artifactId>
        <version>${powermock-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-module-junit4</artifactId>
        <version>${powermock-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-api-mockito</artifactId>
        <version>${powermock-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.powermock</groupId>
        <artifactId>powermock-api-mockito-common</artifactId>
        <version>${powermock-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.jmock</groupId>
        <artifactId>jmock-junit4</artifactId>
        <version>${jmock-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.jmock</groupId>
        <artifactId>jmock-legacy</artifactId>
        <version>${jmock-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-all</artifactId>
        <version>${hamcrest-version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jettison</groupId>
        <artifactId>jettison</artifactId>
        <version>${jettison-version}</version>
    </dependency>

      <dependency>
        <groupId>annogen</groupId>
        <artifactId>annogen</artifactId>
        <version>${annogen-version}</version>
      </dependency>

      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant</artifactId>
        <version>${ant-version}</version>
      </dependency>

      <!-- ACTIVEMQ-WEB Specific Dependencies -->
      <dependency>
        <groupId>com.rometools</groupId>
        <artifactId>rome</artifactId>
        <version>${rome-version}</version>
      </dependency>
      <dependency>
        <groupId>org.fusesource.mqtt-client</groupId>
        <artifactId>mqtt-client</artifactId>
        <version>${mqtt-client-version}</version>
      </dependency>

      <dependency>
        <groupId>p2psockets</groupId>
        <artifactId>p2psockets-core</artifactId>
        <version>${p2psockets-version}</version>
      </dependency>
      <dependency>
        <groupId>jxta</groupId>
        <artifactId>jxta</artifactId>
        <version>${jxta-version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j-version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>${slf4j-version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>${slf4j-version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.geronimo.components</groupId>
        <artifactId>geronimo-transaction</artifactId>
        <version>3.1.4</version>
      </dependency>

      <!-- FTP support for BlobMessages -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons-net-version}</version>
        </dependency>

      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity-version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.servicemix.bundles</groupId>
        <artifactId>org.apache.servicemix.bundles.josql</artifactId>
        <version>${josql-version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <defaultGoal>test</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.activemq.protobuf</groupId>
          <artifactId>activemq-protobuf</artifactId>
          <version>${activemq-protobuf-version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin-version}</version>
          <configuration>
            <tarLongFileMode>gnu</tarLongFileMode>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin-version}</version>
          <configuration>
            <archive>
              <compress>true</compress>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin-version}</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <allowTimestampedSnapshots>false</allowTimestampedSnapshots>
            <preparationGoals>clean install</preparationGoals>
            <goals>deploy</goals>
            <remoteTagging>false</remoteTagging>
            <suppressCommitBeforeTag>false</suppressCommitBeforeTag>
            <tagNameFormat>activemq-@{project.version}</tagNameFormat>
          </configuration>
          <dependencies>
            <dependency>
             <groupId>org.apache.maven.scm</groupId>
             <artifactId>maven-scm-api</artifactId>
             <version>1.8.1</version>
            </dependency>
          <dependency>
             <groupId>org.apache.maven.scm</groupId>
             <artifactId>maven-scm-provider-gitexe</artifactId>
             <version>1.8.1</version>
           </dependency>
         </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin-version}</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <forkCount>1</forkCount>
            <reuseForks>true</reuseForks>
            <argLine>-enableassertions</argLine>
            <failIfNoTests>false</failIfNoTests>
            <systemPropertyVariables>
                <java.awt.headless>true</java.awt.headless>
                <org.apache.activemq.kahaDB.files.skipMetadataUpdate>true</org.apache.activemq.kahaDB.files.skipMetadataUpdate>
            </systemPropertyVariables>
            <argLine>-Xmx512m</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${maven-bundle-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>ianal-maven-plugin</artifactId>
          <version>${ianal-maven-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>javacc-maven-plugin</artifactId>
          <version>${javacc-maven-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${apache-rat-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.eclipse.jetty</groupId>
          <artifactId>jetty-maven-plugin</artifactId>
          <version>${jetty-version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${build-helper-maven-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>cobertura-maven-plugin</artifactId>
          <version>${cobertura-maven-plugin-version}</version>
          <configuration>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-eclipse-plugin</artifactId>
          <version>${maven-eclipse-plugin-version}</version>
          <configuration>
            <downloadSources>true</downloadSources>
            <downloadJavadocs>false</downloadJavadocs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven-war-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin-version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-archiver</artifactId>
          <version>${maven-archiver-version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${maven-plugin-plugin-version}</version>
        </plugin>
        <!--This plugin's configuration is used to store Eclipse m2e settings only.
            It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <versionRange>
                       [2.3.5,)
                    </versionRange>
                    <goals>
                      <goal>cleanVersions</goal>
                      <goal>manifest</goal>
                      <goal>mapping</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.activemq.protobuf</groupId>
                    <artifactId>activemq-protobuf</artifactId>
                    <versionRange>[0.0.0,)</versionRange>
                    <goals>
                      <goal>compile</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.xbean</groupId>
                    <artifactId>maven-xbean-plugin</artifactId>
                    <versionRange>[0.0.0,)</versionRange>
                    <goals>
                      <goal>mapping</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <versionRange>[3.1,)</versionRange>
                    <goals>
                      <goal>descriptor</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-remote-resources-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>process</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>${source-version}</source>
          <target>${target-version}</target>
          <optimize>true</optimize>
          <debug>true</debug>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>ianal-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>verify-legal-files</goal>
            </goals>
            <configuration>
              <strict>true</strict>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <inherited>true</inherited>
        <configuration>
          <instructions>
            <Bundle-Name>${project.artifactId}</Bundle-Name>
            <Bundle-SymbolicName>${activemq.osgi.symbolic.name}</Bundle-SymbolicName>
            <Export-Package>${activemq.osgi.export}</Export-Package>
            <Import-Package>${activemq.osgi.import}</Import-Package>
            <DynamicImport-Package>${activemq.osgi.dynamic.import}</DynamicImport-Package>
            <Private-Package>${activemq.osgi.private.pkg}</Private-Package>
            <Implementation-Title>Apache ActiveMQ</Implementation-Title>
            <Implementation-Version>${project.version}</Implementation-Version>
            <_versionpolicy-impl>[$(version;==;$(@)),$(version;=+;$(@)))</_versionpolicy-impl>
            <_versionpolicy-uses>[$(version;==;$(@)),$(version;+;$(@)))</_versionpolicy-uses>
            <_versionpolicy>[$(version;==;$(@)),$(version;+;$(@)))</_versionpolicy>
            <_failok>${servicemix.osgi.failok}</_failok>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>cleanVersions</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>cleanVersions</goal>
            </goals>
            <configuration>
              <versions>
                <activemq.osgi.version>${project.version}</activemq.osgi.version>
              </versions>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludeSubProjects>false</excludeSubProjects>
          <excludes>
            <exclude>**/*.bin</exclude>
            <exclude>**/*.log</exclude>
            <exclude>**/*.txt</exclude>
            <exclude>**/*.md</exclude>
            <exclude>**/kahadb/**/*.data</exclude>
            <exclude>**/resources/*.xsd</exclude>
            <exclude>**/src/test/resources/keystore</exclude>
            <!-- web, web-console, web-demo files -->
            <exclude>**/webapp/mqtt/mqttws31.js</exclude>
            <exclude>**/webapp/js/dojo.js</exclude>
            <exclude>**/jquery-*.js</exclude>
            <exclude>**/bootstrap.min.css</exclude>
            <exclude>**/bootstrap.min.responsive.css</exclude>
            <exclude>**/prototype.js</exclude>
            <exclude>**/web/behaviour.js</exclude>
            <exclude>**/webapp/js/common.js</exclude>
            <exclude>**/webapp/js/css.js</exclude>
            <exclude>**/webapp/js/standardista-table-sorting.js</exclude>
            <exclude>**/*.data</exclude>
            <exclude>**/webapp/test/assets/*</exclude>
            <exclude>**/*.lck</exclude>
            <exclude>**/*.patch</exclude>
            <exclude>**/*.diff</exclude>
            <!-- plotkit -->
            <exclude>**/webapp/js/plotkit/**/*</exclude>
            <!-- Eclipse files -->
            <exclude>**/.*</exclude>
            <exclude>**/.settings/*</exclude>
            <exclude>**/eclipse-classes/**/*</exclude>
            <exclude>**/target/**/*</exclude>
            <exclude>**/*.iml</exclude>
            <exclude>**/*.ipr</exclude>
            <exclude>**/*.iws</exclude>
            <exclude>**/.idea/**/*</exclude>
            <exclude>.git/**/*</exclude>
            <exclude>**/LevelDB/**/*</exclude>
            <exclude>**/activemq-data/**/*</exclude>
            <exclude>**/dependency-reduced-pom.xml</exclude>
            <exclude>**/*.sln</exclude>
            <exclude>**/*.userprefs</exclude>
            <exclude>**/*.csproj</exclude>
            <exclude>**/mqttws31.js</exclude>
            <exclude>**/stomp.js</exclude>
            <exclude>**/__init__.py</exclude>
            <exclude>**/webapp/decorators/footer.jsp</exclude>
            <exclude>**/docs/img/logo.svg</exclude>
            <exclude>**/testJdbcConfig/**/*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links>
            <link>${javase-javadoc-url}</link>
            <link>${javaee-javadoc-url}</link>
            <link>http://jakarta.apache.org/commons/collections/apidocs-COLLECTIONS_3_0/</link>
            <link>http://jakarta.apache.org/commons/dbcp/apidocs/</link>
            <link>http://jakarta.apache.org/commons/fileupload/apidocs/</link>
            <link>http://hc.apache.org/httpcomponents-client-ga/httpclient/apidocs/</link>
            <link>${slf4j-javadoc-url}</link>
            <link>http://jakarta.apache.org/commons/pool/apidocs/</link>
            <link>http://junit.sourceforge.net/javadoc/</link>
            <link>http://logging.apache.org/log4j/docs/api/</link>
          </links>
          <stylesheetfile>${basedir}/../etc/css/stylesheet.css</stylesheetfile>
          <linksource>true</linksource>
          <maxmemory>2048m</maxmemory>
          <source>${source-version}</source>
          <additionalJOption>-J-Xmx2048m</additionalJOption>
          <!-- necessary for now under the javadocs can be fixed because jdk8 is much stricter -->
          <additionalparam>${javadoc.options}</additionalparam>
        </configuration>
        <executions>
            <execution>
                <id>aggregate</id>
                <phase>site</phase>
                <goals>
                    <goal>aggregate</goal>
                </goals>
            </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <!--
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${maven-surefire-plugin-version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
      </plugin>
      -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <version>${taglist-maven-plugin-version}</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>

    <!-- If you enable this profile, the console message will have a timestamp -->
    <profile>
      <id>consolets</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.fusesource.mvnplugins</groupId>
            <artifactId>maven-consolets-plugin</artifactId>
            <version>1.30</version>
            <configuration>
              <enabled>true</enabled>
            </configuration>
            <executions>
              <execution>
                <phase>validate</phase>
                <goals>
                  <goal>install</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>unstable</id>
      <modules>
      </modules>
    </profile>
    <profile>
      <id>apache-release</id>
      <activation>
        <property>
          <name>apache-release</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${maven-surefire-plugin-version}</version>
            <configuration>
              <test>false</test>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.rat</groupId>
            <artifactId>apache-rat-plugin</artifactId>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>deploy</id>
      <build>
        <defaultGoal>deploy</defaultGoal>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>${maven-source-plugin-version}</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin-version}</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                  <goals>
                    <goal>jar</goal>
                  </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
            <version>${maven-project-info-reports-plugin-version}</version>
            <configuration>
              <dependencyLocationsEnabled>
                false
              </dependencyLocationsEnabled>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!--
      To generate a graph of the project dependencies, run:
      mvn -P graph graph:reactor -Dhide-scope=test -Dhide-transitive=true -Dhide-external=true -Dgraph.label= -Dhide-version=true -Dhide-group-id=true -Dhide-type=true
    -->
    <profile>
      <id>graph</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.fusesource.mvnplugins</groupId>
            <artifactId>maven-graph-plugin</artifactId>
            <version>${maven-graph-plugin-version}</version>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
        <id>jetty9</id>
        <properties>
            <jetty-version>${jetty9-version}</jetty-version>
        </properties>
    </profile>

    <!-- Execute owasp dependency check plugin -->
    <profile>
      <id>owasp</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.owasp</groupId>
            <artifactId>dependency-check-maven</artifactId>
            <version>${owasp-dependency-check-version}</version>
            <configuration>
              <skipProvidedScope>true</skipProvidedScope>
              <skipRuntimeScope>true</skipRuntimeScope>
              <skipSystemScope>true</skipSystemScope>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>aggregate</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- Need to disable doclint for JDK 8 builds so Javadocs work -->
    <profile>
      <id>jdk8-disable-doclint</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <properties>
        <javadoc.options>-Xdoclint:none</javadoc.options>
     </properties>
   </profile>
  </profiles>
</project>

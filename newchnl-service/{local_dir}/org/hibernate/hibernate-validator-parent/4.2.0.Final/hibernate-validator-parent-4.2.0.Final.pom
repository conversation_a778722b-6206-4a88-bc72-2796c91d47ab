<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.hibernate</groupId>
    <artifactId>hibernate-validator-parent</artifactId>
    <packaging>pom</packaging>
    <version>4.2.0.Final</version>
    <name>Hibernate Validator Aggregator</name>
    <url>http://validator.hibernate.org</url>

    <description>
        Aggregator of the Hibernate Validator modules.
    </description>

    <developers>
        <developer>
            <id>epbernard</id>
            <name><PERSON></name>
            <email><EMAIL></email>
            <organization>JBoss, a division of Red Hat</organization>
            <url>http://in.relation.to/Bloggers/Emmanuel</url>
        </developer>
        <developer>
            <id>hardy.ferentschik</id>
            <name><PERSON>rentschik</name>
            <email><EMAIL></email>
            <organization>JBoss, a division of Red Hat</organization>
            <url>http://in.relation.to/Bloggers/Hardy</url>
        </developer>
        <developer>
            <id>gunnar.morling</id>
            <name>Gunnar Morling</name>
            <email><EMAIL></email>
            <organization>Individual</organization>
            <url>http://musingsofaprogrammingaddict.blogspot.com/</url>
        </developer>
        <developer>
            <id>kevinpollet</id>
            <name>Kevin Pollet</name>
            <email><EMAIL></email>
            <organization>Serli</organization>
            <url>http://www.serli.com</url>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>George Gastaldi</name>
            <email><EMAIL></email>
        </contributor>
    </contributors>

    <mailingLists>
        <mailingList>
            <name>hibernate-dev</name>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>

    <modules>
        <module>hibernate-validator</module>
        <module>hibernate-validator-tck-runner</module>
        <module>hibernate-validator-annotation-processor</module>
        <module>hibernate-validator-archetype</module>
        <module>hibernate-validator-distribution</module>
    </modules>

    <properties>
        <slf4jVersion>1.6.1</slf4jVersion>
        <!-- see http://maven.apache.org/general.html -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jdocbook.ignoreTranslations>false</jdocbook.ignoreTranslations>
        <jsr303.tck.version>1.0.5.GA</jsr303.tck.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator-annotation-processor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>1.0.0.GA</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>1.6</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.2</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>2.1.12</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4jVersion}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4jVersion}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${slf4jVersion}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.jtype</groupId>
                <artifactId>jtype</artifactId>
                <version>0.1.1</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.0-api</artifactId>
                <version>1.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-entitymanager</artifactId>
                <version>3.6.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>5.14.2</version>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>3.0</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.2.124</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.jsr303.tck</groupId>
                <artifactId>jsr303-tck</artifactId>
                <version>${jsr303.tck.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.5.2</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.as</groupId>
                <artifactId>jboss-as-arquillian-container-managed</artifactId>
                <version>7.0.0.Beta2</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.jbossas.as7-cdi-tck</groupId>
                <artifactId>jbossas-container</artifactId>
                <version>1.0.0.Alpha1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <extensions>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-webdav</artifactId>
                <version>1.0-beta-2</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.0-beta-1</version>
                <executions>
                    <execution>
                        <id>enforce-java</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <!-- require JDK 1.6 to run the build -->
                            <version>[1.6,)</version>
                        </requireJavaVersion>
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.0</version>
                <configuration>
                    <goals>deploy</goals>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.2</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Implementation-Title>${project.artifactId}</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>${project.groupId}</Implementation-Vendor>
                                <Implementation-Vendor-Id>${project.groupId}</Implementation-Vendor-Id>
                                <Implementation-URL>${project.url}</Implementation-URL>
                                <Specification-Title>Bean Validation</Specification-Title>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.3.2</version>
                    <configuration>
                        <source>1.5</source>
                        <target>1.5</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.4.3</version>
                    <configuration>
                        <forkMode>once</forkMode>
                        <redirectTestOutputToFile>true</redirectTestOutputToFile>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.4.3</version>
                    <executions>
                        <execution>
                            <id>generate-test-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report-only</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <outputDirectory>${project.build.directory}/surefire-reports</outputDirectory>
                        <outputName>test-report</outputName>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.1.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.0</version>
                    <configuration>
                        <preparationGoals>clean install</preparationGoals>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <allowTimestampedSnapshots>true</allowTimestampedSnapshots>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.5</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>jaxb2-maven-plugin</artifactId>
                    <version>1.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>1.4</version>
                </plugin>
                <plugin>
                    <groupId>org.jboss.maven.plugins</groupId>
                    <artifactId>maven-jdocbook-plugin</artifactId>
                    <version>2.2.3</version>
                </plugin>
                <plugin>
                    <groupId>org.jboss.maven.plugins</groupId>
                    <artifactId>maven-jdocbook-style-plugin</artifactId>
                    <version>2.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>2.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>2.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>2.0-alpha-5</version>
                    <executions>
                        <execution>
                            <id>generate-archetype-install</id>
                            <phase>install</phase>
                            <goals>
                                <goal>create-from-project</goal>
                            </goals>
                            <configuration>
                                <archetypePostPhase>install</archetypePostPhase>
                                <propertyFile>archetype.properties</propertyFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>generate-archetype-deploy</id>
                            <phase>deploy</phase>
                            <goals>
                                <goal>create-from-project</goal>
                            </goals>
                            <configuration>
                                <archetypePostPhase>deploy</archetypePostPhase>
                                <propertyFile>archetype.properties</propertyFile>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <docfilessubdirs>true</docfilessubdirs>
                        <javadocDirectory>${project.basedir}/../src/main/javadoc</javadocDirectory>
                        <stylesheetfile>stylesheet.css</stylesheetfile>
                        <bottom>
                            <![CDATA[Copyright &copy; ${project.inceptionYear}-{currentYear} <a href="http://redhat.com">Red Hat Middleware, LLC.</a>  All Rights Reserved]]></bottom>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jboss.maven.plugins</groupId>
                    <artifactId>maven-injection-plugin</artifactId>
                    <version>1.0.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <ciManagement>
        <system>Hudson</system>
        <url>http://hudson.qa.jboss.com/hudson/job/beanvalidation</url>
    </ciManagement>

    <issueManagement>
        <system>JIRA</system>
        <url>http://opensource.atlassian.com/projects/hibernate/browse/HV</url>
    </issueManagement>

    <inceptionYear>2007</inceptionYear>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/hibernate/hibernate-validator.git</connection>
        <developerConnection>scm:git:**************:hibernate/hibernate-validator.git</developerConnection>
        <url>http://github.com/hibernate/hibernate-validator</url>
    </scm>

    <distributionManagement>
        <repository>
            <id>jboss-releases-repository</id>
            <name>JBoss Releases Repository</name>
            <url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
            <id>jboss-snapshots-repository</id>
            <name>JBoss Snapshots Repository</name>
            <url>https://repository.jboss.org/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <!-- HV-396 to allow building with Maven 3 (final version) we have to add the repository configuration to the pom -->
    <repositories>
        <repository>
            <id>jboss-public-repository-group</id>
            <name>JBoss Public Repository Group</name>
            <url>http://repository.jboss.org/nexus/content/groups/public/</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>jboss-public-repository-group</id>
            <name>JBoss Public Repository Group</name>
            <url>http://repository.jboss.org/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
